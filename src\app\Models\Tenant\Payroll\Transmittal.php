<?php

namespace App\Models\Tenant\Payroll;

use App\Models\Core\Traits\StatusRelationship;
use App\Models\Tenant\TenantModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Transmittal extends TenantModel
{
    use HasFactory, StatusRelationship;

    protected $fillable = [
        'department_id',
        'payrun_id',
        'status_id',
        'generated_by',
        'metadata',
        'reference_number',
        'notes',
        'approved_at',
        'approved_by',
        'payroll_period',
        'employee_ids',
        'remarks'
    ];

    protected $casts = [
        'approved_at' => 'datetime',
        'metadata' => 'array',
        'employee_ids' => 'array',
    ];

    public function payrun()
    {
        return $this->belongsTo(Payrun::class);
    }

    public function department()
    {
        return $this->belongsTo(\App\Models\Tenant\Employee\Department::class);
    }

    public function generatedBy()
    {
        return $this->belongsTo(\App\Models\Core\Auth\User::class, 'generated_by');
    }

    public function approvedBy()
    {
        return $this->belongsTo(\App\Models\Core\Auth\User::class, 'approved_by');
    }

    public function payslips()
    {
        return $this->hasMany(Payslip::class, 'payrun_id', 'payrun_id');
    }

    public function getEmployeesAttribute()
    {
        if (!$this->employee_ids) {
            return collect();
        }

        return \App\Models\Core\Auth\User::whereIn('id', $this->employee_ids)->get();
    }
}
