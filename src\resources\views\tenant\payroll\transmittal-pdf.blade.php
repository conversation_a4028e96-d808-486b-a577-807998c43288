<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Transmittal Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .company-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .company-address {
            font-size: 12px;
            font-style: italic;
            margin-bottom: 20px;
        }
        .department {
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 20px;
        }
        .period {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        th, td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
        }
        .total-row {
            font-weight: bold;
        }
        .signature-section {
            margin-top: 50px;
        }
        .signature-box {
            border: 1px solid #000;
            height: 80px;
            width: 300px;
            margin-bottom: 10px;
        }
        .signature-label {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .prepared-by {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">{{ settings('tenant_name', 'STA. CATALINA TOLONG MILLING CORPORATION') }}</div>
        <div class="company-address">{{ settings('company_address', 'Sibo, Sicopong Brgy. Caranoche, Sta. Catalina, Negros Oriental') }}</div>
    </div>

    <div class="department">{{ $transmittal->department->name ?? 'ELECTRICAL' }}</div>

    <div class="period">
        @php
            $periodFrom = $transmittal->metadata['period_from'] ?? null;
            $periodTo = $transmittal->metadata['period_to'] ?? null;
            $periodText = 'PAYROLL COVERED AS OF ' . strtoupper($transmittal->payroll_period);
            if ($periodFrom && $periodTo) {
                $periodText = 'PAYROLL COVERED AS OF ' . strtoupper(date('M d', strtotime($periodFrom)) . '-' . date('M d, Y', strtotime($periodTo)));
            }
        @endphp
        {{ $periodText }}
    </div>

    <table>
        <thead>
            <tr>
                <th>EMPLOYEE<br>NUMBER</th>
                <th>NAME</th>
                <th>DESIGNATION</th>
                <th>TOTAL SALARY</th>
            </tr>
        </thead>
        <tbody>
            @php
                $totalSalary = 0;
            @endphp
            @foreach($transmittal->payrun->payslips as $payslip)
                @php
                    $totalSalary += $payslip->net_salary;
                @endphp
                <tr>
                    <td>{{ $payslip->user->employee_id ?? $payslip->user->id }}</td>
                    <td>{{ $payslip->user->full_name ?? '-' }}</td>
                    <td>{{ optional($payslip->user->designation)->name ?? 'N/A' }}</td>
                    <td style="text-align: right;">{{ number_format($payslip->net_salary, 2) }}</td>
                </tr>
            @endforeach
            <tr class="total-row">
                <td colspan="3" style="text-align: center;">TOTAL</td>
                <td style="text-align: right;">{{ number_format($totalSalary, 2) }}</td>
            </tr>
        </tbody>
    </table>

    <div class="signature-section">
        <div class="signature-label">Prepared By:</div>
        <div class="signature-box"></div>
        <div class="prepared-by">
            @php
                $preparedBy = $transmittal->metadata['prepared_by'] ?? 'Mae N. Tembrevilla';
                $preparedByTitle = $transmittal->metadata['prepared_by_title'] ?? 'Payroll Analyst';
            @endphp
            <div>Via {{ $preparedBy }}</div>
            <div>{{ $preparedByTitle }}</div>
        </div>
    </div>
</body>
</html>
