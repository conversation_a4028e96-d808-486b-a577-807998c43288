<?php

namespace App\Http\Controllers\Tenant\Payroll;

use App\Export\TransmittalExport;
use App\Filters\Tenant\TransmittalFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Tenant\Payroll\TransmittalRequest;
use App\Models\Tenant\Payroll\Payrun;
use App\Models\Tenant\Payroll\Transmittal;
use App\Repositories\Core\Status\StatusRepository;
use App\Services\Tenant\Payroll\TransmittalService;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class TransmittalController extends Controller
{
    public function __construct(TransmittalService $service, TransmittalFilter $filter)
    {
        $this->service = $service;
        $this->filter = $filter;
    }

    public function index()
    {
        return $this->service
            ->filters($this->filter)
            ->with(['payrun', 'department', 'generatedBy', 'approvedBy', 'status'])
            ->latest()
            ->paginate(request()->get('per_page', 10));
    }

    public function store(TransmittalRequest $request)
    {
        $statusActive = resolve(StatusRepository::class)->transmittalActive();
        $validated = $request->validated();

        // Get employees from the selected payrun
        $payrun = Payrun::with('payslips.user')->find($validated['payrun_id']);
        $employeeIds = $payrun->payslips->pluck('user.id')->toArray();

        $transmittal = $this->service
            ->setAttributes([
                'department_id' => $validated['department_id'],
                'payrun_id' => $validated['payrun_id'],
                'payroll_period' => $validated['payroll_period'],
                'reference_number' => $validated['reference_number'] ?? null,
                'notes' => $validated['notes'] ?? null,
                'remarks' => $validated['remarks'] ?? null,
                'employee_ids' => $employeeIds,
                'metadata' => [
                    'title' => $validated['title'] ?? null,
                    'prepared_by' => $validated['prepared_by'] ?? null,
                    'prepared_by_title' => $validated['prepared_by_title'] ?? null,
                    'period_from' => $validated['period_from'] ?? null,
                    'period_to' => $validated['period_to'] ?? null,
                ],
                'status_id' => $statusActive,
                'generated_by' => auth()->id()
            ])
            ->save();

        return created_responses('transmittal');
    }

    public function show(Transmittal $transmittal)
    {
        return $transmittal->load(['payrun.payslips.user.department', 'department', 'generatedBy', 'approvedBy', 'status']);
    }

    public function update(TransmittalRequest $request, Transmittal $transmittal)
    {
        $validated = $request->validated();

        // Get employees from the selected payrun if payrun changed
        $employeeIds = $transmittal->employee_ids;
        if (isset($validated['payrun_id']) && $validated['payrun_id'] != $transmittal->payrun_id) {
            $payrun = Payrun::with('payslips.user')->find($validated['payrun_id']);
            $employeeIds = $payrun->payslips->pluck('user.id')->toArray();
        }

        $this->service
            ->setModel($transmittal)
            ->setAttributes([
                'department_id' => $validated['department_id'] ?? $transmittal->department_id,
                'payrun_id' => $validated['payrun_id'] ?? $transmittal->payrun_id,
                'payroll_period' => $validated['payroll_period'] ?? $transmittal->payroll_period,
                'reference_number' => $validated['reference_number'] ?? $transmittal->reference_number,
                'notes' => $validated['notes'] ?? $transmittal->notes,
                'remarks' => $validated['remarks'] ?? $transmittal->remarks,
                'employee_ids' => $employeeIds,
                'metadata' => array_merge($transmittal->metadata ?? [], [
                    'title' => $validated['title'] ?? $transmittal->metadata['title'] ?? null,
                    'prepared_by' => $validated['prepared_by'] ?? $transmittal->metadata['prepared_by'] ?? null,
                    'prepared_by_title' => $validated['prepared_by_title'] ?? $transmittal->metadata['prepared_by_title'] ?? null,
                    'period_from' => $validated['period_from'] ?? $transmittal->metadata['period_from'] ?? null,
                    'period_to' => $validated['period_to'] ?? $transmittal->metadata['period_to'] ?? null,
                ])
            ])
            ->save();

        return updated_responses('transmittal');
    }

    public function destroy(Transmittal $transmittal)
    {
        $transmittal->delete();
        return deleted_responses('transmittal');
    }

    public function exportPdf(Transmittal $transmittal)
    {
        $transmittal->load(['payrun.payslips.user.department', 'createdBy']);

        $pdf = Pdf::loadView('tenant.payroll.transmittal-pdf', compact('transmittal'));

        return $pdf->download('transmittal-' . $transmittal->id . '-' . date('Y-m-d') . '.pdf');
    }

    public function exportCsv(Transmittal $transmittal)
    {
        $transmittal->load(['payrun.payslips.user.department', 'createdBy']);

        $fileName = 'transmittal-' . $transmittal->id . '-' . date('Y-m-d') . '.csv';

        return (new TransmittalExport($transmittal))->download($fileName, \Maatwebsite\Excel\Excel::CSV);
    }

    public function getPayruns()
    {
        return Payrun::with(['payslips.user.department'])
            ->whereHas('payslips')
            ->latest()
            ->get()
            ->map(function ($payrun) {
                return [
                    'id' => $payrun->id,
                    'name' => $payrun->name,
                    'uid' => $payrun->uid,
                    'payslips_count' => $payrun->payslips->count(),
                    'total_salary' => $payrun->payslips->sum('net_salary'),
                    'period' => $payrun->payslips->first()?->period,
                    'start_date' => $payrun->payslips->first()?->start_date,
                    'end_date' => $payrun->payslips->first()?->end_date,
                ];
            });
    }

    public function getDepartments()
    {
        return \App\Models\Tenant\Employee\Department::where('status_id', resolve(StatusRepository::class)->departmentActive())
            ->get(['id', 'name'])
            ->map(function ($department) {
                return [
                    'id' => $department->id,
                    'name' => $department->name,
                ];
            });
    }
}
