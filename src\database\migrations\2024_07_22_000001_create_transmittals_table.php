<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('transmittals', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('department');
            $table->date('period_from');
            $table->date('period_to');
            $table->string('prepared_by');
            $table->string('prepared_by_title');
            $table->unsignedBigInteger('payrun_id');
            $table->unsignedBigInteger('status_id');
            $table->unsignedBigInteger('created_by');
            $table->timestamps();

            $table->foreign('payrun_id')->references('id')->on('payruns')->onDelete('cascade');
            $table->foreign('status_id')->references('id')->on('statuses')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('transmittals');
    }
};
