<?php

namespace App\Http\Requests\Tenant\Payroll;

use App\Http\Requests\BaseRequest;

class TransmittalRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'title' => 'nullable|string|max:255',
            'department_id' => 'required|exists:departments,id',
            'payrun_id' => 'required|exists:payruns,id',
            'payroll_period' => 'required|string|max:255',
            'reference_number' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
            'remarks' => 'nullable|string',
            'period_from' => 'nullable|date',
            'period_to' => 'nullable|date|after_or_equal:period_from',
            'prepared_by' => 'nullable|string|max:255',
            'prepared_by_title' => 'nullable|string|max:255',
        ];
    }

    public function messages()
    {
        return [
            'department_id.required' => 'The department is required.',
            'department_id.exists' => 'The selected department is invalid.',
            'payrun_id.required' => 'Please select a payrun.',
            'payrun_id.exists' => 'The selected payrun is invalid.',
            'payroll_period.required' => 'The payroll period is required.',
            'period_to.after_or_equal' => 'The period to date must be after or equal to the period from date.',
        ];
    }
}
