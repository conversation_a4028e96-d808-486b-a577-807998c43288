<template>
    <div class="content-wrapper">
        <app-page-top-section :title="$t('for_signature')">
            <app-default-button
                :title="$fieldTitle('add', 'signature_request', true)"
                v-if="$can('create_payroll_for_signature')"
                @click="openModal()"
            />
        </app-page-top-section>

        <div class="card border-0">
            <div class="card-header d-flex align-items-center justify-content-between primary-card-color">
                <h5 class="card-title mb-0">{{ $t('for_signature_list') }}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12">
                        <p class="text-muted">{{ $t('for_signature_description') }}</p>
                        <!-- Add your for signature content here -->
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle mr-2"></i>
                            {{ $t('for_signature_coming_soon') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "PayrollForSignature",
    data() {
        return {
            isModalActive: false,
            loading: false,
        }
    },
    mounted() {
        // Component initialization
    },
    methods: {
        openModal() {
            this.isModalActive = true;
        },
    }
}
</script>

<style scoped>
/* Remove custom padding to use default layout */
</style>
