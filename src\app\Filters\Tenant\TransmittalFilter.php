<?php

namespace App\Filters\Tenant;

use App\Filters\Core\traits\NameFilter;
use App\Filters\Core\traits\SearchFilterTrait;
use App\Filters\FilterBuilder;
use Illuminate\Database\Eloquent\Builder;

class TransmittalFilter extends FilterBuilder
{
    use SearchFilterTrait, NameFilter;

    public function department($department = null)
    {
        $department = $department ?: request('department');

        $this->builder->when($department, function (Builder $builder) use ($department) {
            $builder->where('department', 'LIKE', "%$department%");
        });
    }

    public function payrun($payrun = null)
    {
        $payrun = $payrun ?: request('payrun');

        $this->builder->when($payrun, function (Builder $builder) use ($payrun) {
            $builder->where('payrun_id', $payrun);
        });
    }

    public function period($period = null)
    {
        $period = $period ?: request('period');

        $this->builder->when($period, function (Builder $builder) use ($period) {
            $dates = explode(' to ', $period);
            if (count($dates) == 2) {
                $builder->whereBetween('period_from', [$dates[0], $dates[1]]);
            }
        });
    }
}
