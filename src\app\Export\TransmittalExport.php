<?php

namespace App\Export;

use App\Helpers\Traits\DateTimeHelper;
use App\Models\Tenant\Payroll\Transmittal;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;

class TransmittalExport implements FromArray, WithHeadings, ShouldAutoSize
{
    use Exportable, DateTimeHelper;

    private Transmittal $transmittal;

    public function __construct(Transmittal $transmittal)
    {
        $this->transmittal = $transmittal;
    }

    public function headings(): array
    {
        $companyName = settings('tenant_name', 'STA. CATALINA TOLONG MILLING CORPORATION');
        $companyAddress = settings('company_address', 'Sibo, Sicopong Brgy. Caranoche, Sta. Catalina, Negros Oriental');

        $departmentName = $this->transmittal->department->name ?? 'ELECTRICAL';
        $periodFrom = $this->transmittal->metadata['period_from'] ?? null;
        $periodTo = $this->transmittal->metadata['period_to'] ?? null;

        $periodText = 'PAYROLL COVERED AS OF ' . strtoupper($this->transmittal->payroll_period);
        if ($periodFrom && $periodTo) {
            $periodText = 'PAYROLL COVERED AS OF ' . strtoupper(date('M d', strtotime($periodFrom)) . '-' . date('M d, Y', strtotime($periodTo)));
        }

        return [
            [$companyName],
            [$companyAddress],
            [''],
            [$departmentName],
            [''],
            [$periodText],
            ['EMPLOYEE NUMBER', 'NAME', 'DESIGNATION', 'TOTAL SALARY'],
        ];
    }

    public function array(): array
    {
        $payslips = $this->transmittal->payrun->payslips()->with(['user.department'])->get();

        $data = [];

        // Add payslip data
        foreach ($payslips as $payslip) {
            $data[] = [
                $payslip->user->employee_id ?? $payslip->user->id,
                $payslip->user->full_name ?? '-',
                $payslip->user->designation->name ?? 'N/A',
                number_format($payslip->net_salary, 2)
            ];
        }

        // Add total row
        $totalSalary = $payslips->sum('net_salary');
        $data[] = ['', '', 'TOTAL', number_format($totalSalary, 2)];

        // Add empty rows for signature
        $data[] = ['', '', '', ''];
        $data[] = ['', '', '', ''];
        $data[] = ['Prepared By:', '', '', ''];
        $data[] = ['', '', '', ''];
        $data[] = ['', '', '', ''];

        $preparedBy = $this->transmittal->metadata['prepared_by'] ?? 'Mae N. Tembrevilla';
        $preparedByTitle = $this->transmittal->metadata['prepared_by_title'] ?? 'Payroll Analyst';

        $data[] = ['Via ' . $preparedBy, '', '', ''];
        $data[] = [$preparedByTitle, '', '', ''];

        return $data;
    }
}
