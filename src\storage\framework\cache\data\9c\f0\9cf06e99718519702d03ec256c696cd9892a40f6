1753235905O:25:"App\Models\Core\Auth\User":35:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"users";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:15:{s:2:"id";i:1;s:10:"first_name";s:4:"John";s:9:"last_name";s:3:"Doe";s:5:"email";s:14:"<EMAIL>";s:8:"password";s:60:"$2y$10$NbtZSG9vizLM1ZGLDwfExeR1ltGPB6aaC7s85YNv1JNwd.drYHh1e";s:14:"signature_path";s:63:"storage/signatures/nkufZZcVlNpOBmyH1DrcJwfd0ikrWxtfCv7WtjGh.png";s:13:"last_login_at";N;s:10:"created_by";N;s:9:"status_id";i:1;s:16:"invitation_token";N;s:14:"remember_token";N;s:10:"created_at";s:19:"2025-07-10 02:38:06";s:10:"updated_at";s:19:"2025-07-10 06:05:14";s:10:"deleted_at";N;s:14:"is_in_employee";i:1;}s:11:" * original";a:15:{s:2:"id";i:1;s:10:"first_name";s:4:"John";s:9:"last_name";s:3:"Doe";s:5:"email";s:14:"<EMAIL>";s:8:"password";s:60:"$2y$10$NbtZSG9vizLM1ZGLDwfExeR1ltGPB6aaC7s85YNv1JNwd.drYHh1e";s:14:"signature_path";s:63:"storage/signatures/nkufZZcVlNpOBmyH1DrcJwfd0ikrWxtfCv7WtjGh.png";s:13:"last_login_at";N;s:10:"created_by";N;s:9:"status_id";i:1;s:16:"invitation_token";N;s:14:"remember_token";N;s:10:"created_at";s:19:"2025-07-10 02:38:06";s:10:"updated_at";s:19:"2025-07-10 06:05:14";s:10:"deleted_at";N;s:14:"is_in_employee";i:1;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:9:"full_name";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:14:"profilePicture";N;s:5:"roles";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:25:"App\Models\Core\Auth\Role":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"roles";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:1:{s:2:"id";i:1;}s:11:" * original";a:3:{s:2:"id";i:1;s:13:"pivot_user_id";i:1;s:13:"pivot_role_id";i:1;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"is_admin";s:7:"boolean";s:10:"is_default";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:2:{s:5:"pivot";O:44:"Illuminate\Database\Eloquent\Relations\Pivot":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:9:"role_user";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:2:{s:7:"user_id";i:1;s:7:"role_id";i:1;}s:11:" * original";a:2:{s:7:"user_id";i:1;s:7:"role_id";i:1;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";O:25:"App\Models\Core\Auth\User":35:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"users";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:0;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:0:{}s:11:" * original";a:0:{}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:9:"full_name";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:2:{i:0;s:8:"password";i:1;s:14:"remember_token";}s:10:" * visible";a:0:{}s:11:" * fillable";a:10:{i:0;s:10:"first_name";i:1;s:9:"last_name";i:2;s:5:"email";i:3;s:8:"password";i:4;s:6:"active";i:5;s:13:"last_login_at";i:6;s:10:"created_by";i:7;s:9:"status_id";i:8;s:16:"invitation_token";i:9;s:14:"is_in_employee";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:20:" * rememberTokenName";s:14:"remember_token";s:16:" * forceDeleting";b:0;s:16:" * oldAttributes";a:0:{}s:25:"enableLoggingModelsEvents";b:1;s:14:" * accessToken";N;}s:13:" * foreignKey";s:7:"user_id";s:13:" * relatedKey";s:7:"role_id";}s:11:"permissions";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:9:{i:0;O:31:"App\Models\Core\Auth\Permission":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:11:"permissions";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:160;s:7:"type_id";i:2;s:4:"name";s:16:"view_transmittal";s:10:"group_name";s:7:"payroll";s:10:"created_at";s:19:"2025-07-22 00:57:14";s:10:"updated_at";s:19:"2025-07-22 00:57:14";}s:11:" * original";a:9:{s:2:"id";i:160;s:7:"type_id";i:2;s:4:"name";s:16:"view_transmittal";s:10:"group_name";s:7:"payroll";s:10:"created_at";s:19:"2025-07-22 00:57:14";s:10:"updated_at";s:19:"2025-07-22 00:57:14";s:13:"pivot_role_id";i:1;s:19:"pivot_permission_id";i:160;s:10:"pivot_meta";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:15:"translated_name";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:40:"App\Models\Core\Auth\RolePermissionPivot":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:15:"role_permission";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:7:"role_id";i:1;s:13:"permission_id";i:160;s:4:"meta";N;}s:11:" * original";a:3:{s:7:"role_id";i:1;s:13:"permission_id";i:160;s:4:"meta";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:4:"meta";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";O:25:"App\Models\Core\Auth\Role":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"roles";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:0;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:0:{}s:11:" * original";a:0:{}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:8:"is_admin";s:7:"boolean";s:10:"is_default";s:7:"boolean";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:8:"is_admin";i:2;s:10:"is_default";i:3;s:7:"type_id";i:4;s:10:"created_by";i:5;s:5:"alias";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * oldAttributes";a:0:{}s:25:"enableLoggingModelsEvents";b:1;}s:13:" * foreignKey";s:7:"role_id";s:13:" * relatedKey";s:13:"permission_id";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:7:"type_id";i:1;s:4:"name";i:2;s:10:"group_name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * oldAttributes";a:0:{}s:25:"enableLoggingModelsEvents";b:1;}i:1;O:31:"App\Models\Core\Auth\Permission":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:11:"permissions";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:161;s:7:"type_id";i:2;s:4:"name";s:20:"generate_transmittal";s:10:"group_name";s:7:"payroll";s:10:"created_at";s:19:"2025-07-22 00:57:14";s:10:"updated_at";s:19:"2025-07-22 00:57:14";}s:11:" * original";a:9:{s:2:"id";i:161;s:7:"type_id";i:2;s:4:"name";s:20:"generate_transmittal";s:10:"group_name";s:7:"payroll";s:10:"created_at";s:19:"2025-07-22 00:57:14";s:10:"updated_at";s:19:"2025-07-22 00:57:14";s:13:"pivot_role_id";i:1;s:19:"pivot_permission_id";i:161;s:10:"pivot_meta";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:15:"translated_name";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:40:"App\Models\Core\Auth\RolePermissionPivot":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:15:"role_permission";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:7:"role_id";i:1;s:13:"permission_id";i:161;s:4:"meta";N;}s:11:" * original";a:3:{s:7:"role_id";i:1;s:13:"permission_id";i:161;s:4:"meta";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:4:"meta";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:258;s:13:" * foreignKey";s:7:"role_id";s:13:" * relatedKey";s:13:"permission_id";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:7:"type_id";i:1;s:4:"name";i:2;s:10:"group_name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * oldAttributes";a:0:{}s:25:"enableLoggingModelsEvents";b:1;}i:2;O:31:"App\Models\Core\Auth\Permission":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:11:"permissions";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:162;s:7:"type_id";i:2;s:4:"name";s:18:"export_transmittal";s:10:"group_name";s:7:"payroll";s:10:"created_at";s:19:"2025-07-22 00:57:14";s:10:"updated_at";s:19:"2025-07-22 00:57:14";}s:11:" * original";a:9:{s:2:"id";i:162;s:7:"type_id";i:2;s:4:"name";s:18:"export_transmittal";s:10:"group_name";s:7:"payroll";s:10:"created_at";s:19:"2025-07-22 00:57:14";s:10:"updated_at";s:19:"2025-07-22 00:57:14";s:13:"pivot_role_id";i:1;s:19:"pivot_permission_id";i:162;s:10:"pivot_meta";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:15:"translated_name";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:40:"App\Models\Core\Auth\RolePermissionPivot":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:15:"role_permission";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:7:"role_id";i:1;s:13:"permission_id";i:162;s:4:"meta";N;}s:11:" * original";a:3:{s:7:"role_id";i:1;s:13:"permission_id";i:162;s:4:"meta";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:4:"meta";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:258;s:13:" * foreignKey";s:7:"role_id";s:13:" * relatedKey";s:13:"permission_id";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:7:"type_id";i:1;s:4:"name";i:2;s:10:"group_name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * oldAttributes";a:0:{}s:25:"enableLoggingModelsEvents";b:1;}i:3;O:31:"App\Models\Core\Auth\Permission":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:11:"permissions";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:163;s:7:"type_id";i:2;s:4:"name";s:18:"delete_transmittal";s:10:"group_name";s:7:"payroll";s:10:"created_at";s:19:"2025-07-22 00:57:14";s:10:"updated_at";s:19:"2025-07-22 00:57:14";}s:11:" * original";a:9:{s:2:"id";i:163;s:7:"type_id";i:2;s:4:"name";s:18:"delete_transmittal";s:10:"group_name";s:7:"payroll";s:10:"created_at";s:19:"2025-07-22 00:57:14";s:10:"updated_at";s:19:"2025-07-22 00:57:14";s:13:"pivot_role_id";i:1;s:19:"pivot_permission_id";i:163;s:10:"pivot_meta";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:15:"translated_name";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:40:"App\Models\Core\Auth\RolePermissionPivot":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:15:"role_permission";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:7:"role_id";i:1;s:13:"permission_id";i:163;s:4:"meta";N;}s:11:" * original";a:3:{s:7:"role_id";i:1;s:13:"permission_id";i:163;s:4:"meta";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:4:"meta";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:258;s:13:" * foreignKey";s:7:"role_id";s:13:" * relatedKey";s:13:"permission_id";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:7:"type_id";i:1;s:4:"name";i:2;s:10:"group_name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * oldAttributes";a:0:{}s:25:"enableLoggingModelsEvents";b:1;}i:4;O:31:"App\Models\Core\Auth\Permission":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:11:"permissions";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:164;s:7:"type_id";i:2;s:4:"name";s:14:"view_signature";s:10:"group_name";s:7:"payroll";s:10:"created_at";s:19:"2025-07-22 00:57:14";s:10:"updated_at";s:19:"2025-07-22 00:57:14";}s:11:" * original";a:9:{s:2:"id";i:164;s:7:"type_id";i:2;s:4:"name";s:14:"view_signature";s:10:"group_name";s:7:"payroll";s:10:"created_at";s:19:"2025-07-22 00:57:14";s:10:"updated_at";s:19:"2025-07-22 00:57:14";s:13:"pivot_role_id";i:1;s:19:"pivot_permission_id";i:164;s:10:"pivot_meta";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:15:"translated_name";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:40:"App\Models\Core\Auth\RolePermissionPivot":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:15:"role_permission";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:7:"role_id";i:1;s:13:"permission_id";i:164;s:4:"meta";N;}s:11:" * original";a:3:{s:7:"role_id";i:1;s:13:"permission_id";i:164;s:4:"meta";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:4:"meta";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:258;s:13:" * foreignKey";s:7:"role_id";s:13:" * relatedKey";s:13:"permission_id";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:7:"type_id";i:1;s:4:"name";i:2;s:10:"group_name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * oldAttributes";a:0:{}s:25:"enableLoggingModelsEvents";b:1;}i:5;O:31:"App\Models\Core\Auth\Permission":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:11:"permissions";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:165;s:7:"type_id";i:2;s:4:"name";s:17:"manage_signatures";s:10:"group_name";s:7:"payroll";s:10:"created_at";s:19:"2025-07-22 00:57:14";s:10:"updated_at";s:19:"2025-07-22 00:57:14";}s:11:" * original";a:9:{s:2:"id";i:165;s:7:"type_id";i:2;s:4:"name";s:17:"manage_signatures";s:10:"group_name";s:7:"payroll";s:10:"created_at";s:19:"2025-07-22 00:57:14";s:10:"updated_at";s:19:"2025-07-22 00:57:14";s:13:"pivot_role_id";i:1;s:19:"pivot_permission_id";i:165;s:10:"pivot_meta";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:15:"translated_name";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:40:"App\Models\Core\Auth\RolePermissionPivot":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:15:"role_permission";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:7:"role_id";i:1;s:13:"permission_id";i:165;s:4:"meta";N;}s:11:" * original";a:3:{s:7:"role_id";i:1;s:13:"permission_id";i:165;s:4:"meta";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:4:"meta";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:258;s:13:" * foreignKey";s:7:"role_id";s:13:" * relatedKey";s:13:"permission_id";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:7:"type_id";i:1;s:4:"name";i:2;s:10:"group_name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * oldAttributes";a:0:{}s:25:"enableLoggingModelsEvents";b:1;}i:6;O:31:"App\Models\Core\Auth\Permission":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:11:"permissions";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:166;s:7:"type_id";i:2;s:4:"name";s:18:"view_consolidation";s:10:"group_name";s:7:"payroll";s:10:"created_at";s:19:"2025-07-22 00:57:14";s:10:"updated_at";s:19:"2025-07-22 00:57:14";}s:11:" * original";a:9:{s:2:"id";i:166;s:7:"type_id";i:2;s:4:"name";s:18:"view_consolidation";s:10:"group_name";s:7:"payroll";s:10:"created_at";s:19:"2025-07-22 00:57:14";s:10:"updated_at";s:19:"2025-07-22 00:57:14";s:13:"pivot_role_id";i:1;s:19:"pivot_permission_id";i:166;s:10:"pivot_meta";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:15:"translated_name";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:40:"App\Models\Core\Auth\RolePermissionPivot":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:15:"role_permission";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:7:"role_id";i:1;s:13:"permission_id";i:166;s:4:"meta";N;}s:11:" * original";a:3:{s:7:"role_id";i:1;s:13:"permission_id";i:166;s:4:"meta";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:4:"meta";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:258;s:13:" * foreignKey";s:7:"role_id";s:13:" * relatedKey";s:13:"permission_id";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:7:"type_id";i:1;s:4:"name";i:2;s:10:"group_name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * oldAttributes";a:0:{}s:25:"enableLoggingModelsEvents";b:1;}i:7;O:31:"App\Models\Core\Auth\Permission":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:11:"permissions";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:167;s:7:"type_id";i:2;s:4:"name";s:22:"generate_consolidation";s:10:"group_name";s:7:"payroll";s:10:"created_at";s:19:"2025-07-22 00:57:14";s:10:"updated_at";s:19:"2025-07-22 00:57:14";}s:11:" * original";a:9:{s:2:"id";i:167;s:7:"type_id";i:2;s:4:"name";s:22:"generate_consolidation";s:10:"group_name";s:7:"payroll";s:10:"created_at";s:19:"2025-07-22 00:57:14";s:10:"updated_at";s:19:"2025-07-22 00:57:14";s:13:"pivot_role_id";i:1;s:19:"pivot_permission_id";i:167;s:10:"pivot_meta";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:15:"translated_name";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:40:"App\Models\Core\Auth\RolePermissionPivot":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:15:"role_permission";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:7:"role_id";i:1;s:13:"permission_id";i:167;s:4:"meta";N;}s:11:" * original";a:3:{s:7:"role_id";i:1;s:13:"permission_id";i:167;s:4:"meta";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:4:"meta";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:258;s:13:" * foreignKey";s:7:"role_id";s:13:" * relatedKey";s:13:"permission_id";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:7:"type_id";i:1;s:4:"name";i:2;s:10:"group_name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * oldAttributes";a:0:{}s:25:"enableLoggingModelsEvents";b:1;}i:8;O:31:"App\Models\Core\Auth\Permission":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:11:"permissions";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:6:{s:2:"id";i:168;s:7:"type_id";i:2;s:4:"name";s:20:"export_consolidation";s:10:"group_name";s:7:"payroll";s:10:"created_at";s:19:"2025-07-22 00:57:14";s:10:"updated_at";s:19:"2025-07-22 00:57:14";}s:11:" * original";a:9:{s:2:"id";i:168;s:7:"type_id";i:2;s:4:"name";s:20:"export_consolidation";s:10:"group_name";s:7:"payroll";s:10:"created_at";s:19:"2025-07-22 00:57:14";s:10:"updated_at";s:19:"2025-07-22 00:57:14";s:13:"pivot_role_id";i:1;s:19:"pivot_permission_id";i:168;s:10:"pivot_meta";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:15:"translated_name";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:5:"pivot";O:40:"App\Models\Core\Auth\RolePermissionPivot":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:15:"role_permission";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:0;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:3:{s:7:"role_id";i:1;s:13:"permission_id";i:168;s:4:"meta";N;}s:11:" * original";a:3:{s:7:"role_id";i:1;s:13:"permission_id";i:168;s:4:"meta";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:4:"meta";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:11:"pivotParent";r:258;s:13:" * foreignKey";s:7:"role_id";s:13:" * relatedKey";s:13:"permission_id";}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:3:{i:0;s:7:"type_id";i:1;s:4:"name";i:2;s:10:"group_name";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * oldAttributes";a:0:{}s:25:"enableLoggingModelsEvents";b:1;}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:6:{i:0;s:4:"name";i:1;s:8:"is_admin";i:2;s:10:"is_default";i:3;s:7:"type_id";i:4;s:10:"created_by";i:5;s:5:"alias";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * oldAttributes";a:0:{}s:25:"enableLoggingModelsEvents";b:1;}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:2:{i:0;s:8:"password";i:1;s:14:"remember_token";}s:10:" * visible";a:0:{}s:11:" * fillable";a:10:{i:0;s:10:"first_name";i:1;s:9:"last_name";i:2;s:5:"email";i:3;s:8:"password";i:4;s:6:"active";i:5;s:13:"last_login_at";i:6;s:10:"created_by";i:7;s:9:"status_id";i:8;s:16:"invitation_token";i:9;s:14:"is_in_employee";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:20:" * rememberTokenName";s:14:"remember_token";s:16:" * forceDeleting";b:0;s:16:" * oldAttributes";a:0:{}s:25:"enableLoggingModelsEvents";b:1;s:14:" * accessToken";N;}