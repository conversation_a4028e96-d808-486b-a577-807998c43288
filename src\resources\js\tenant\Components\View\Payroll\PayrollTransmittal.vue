<template>
    <div class="content-wrapper">
        <app-page-top-section :title="$t('transmittal')">
            <app-default-button
                :title="$fieldTitle('add', 'transmittal', true)"
                v-if="$can('create_payroll_transmittal')"
                @click="openModal()"
            />

        </app-page-top-section>


        <div class="card border-0">
            <div class="card-header d-flex align-items-center justify-content-between primary-card-color">
                <h5 class="card-title mb-0">{{ $t('transmittal_list') }}</h5>
            </div>
            <div class="card-body">
                <app-table
                    id="transmittal-table"
                    :options="options"
                    @action="triggerActions"
                />
            </div>
        </div>

        <!-- Create/Edit Modal -->
        <app-modal
            v-if="isModalActive"
            v-model="isModalActive"
            :title="isEdit ? $t('edit_transmittal') : $t('create_transmittal')"
            modal-size="large"
            @close-modal="closeModal"
        >
            <app-overlay-loader v-if="loading"/>
            <form @submit.prevent="submitForm" v-else>
                <div class="row">
                    <div class="col-md-6">
                        <app-form-group
                            :label="$t('title')"
                            :placeholder="$t('enter_transmittal_title')"
                            v-model="form.title"
                            :error-message="errors.title"
                        />
                    </div>
                    <div class="col-md-6">
                        <app-form-group
                            :label="$t('department')"
                            type="select"
                            :list="departmentOptions"
                            v-model="form.department_id"
                            :error-message="errors.department_id"
                            required
                        />
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <app-form-group
                            :label="$t('payroll_period')"
                            :placeholder="$t('enter_payroll_period')"
                            v-model="form.payroll_period"
                            :error-message="errors.payroll_period"
                            required
                        />
                    </div>
                    <div class="col-md-6">
                        <app-form-group
                            :label="$t('reference_number')"
                            :placeholder="$t('enter_reference_number')"
                            v-model="form.reference_number"
                            :error-message="errors.reference_number"
                        />
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <app-form-group
                            :label="$t('period_from')"
                            type="date"
                            v-model="form.period_from"
                            :error-message="errors.period_from"
                        />
                    </div>
                    <div class="col-md-6">
                        <app-form-group
                            :label="$t('period_to')"
                            type="date"
                            v-model="form.period_to"
                            :error-message="errors.period_to"
                        />
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <app-form-group
                            :label="$t('prepared_by')"
                            :placeholder="$t('enter_prepared_by')"
                            v-model="form.prepared_by"
                            :error-message="errors.prepared_by"
                            required
                        />
                    </div>
                    <div class="col-md-6">
                        <app-form-group
                            :label="$t('prepared_by_title')"
                            :placeholder="$t('enter_prepared_by_title')"
                            v-model="form.prepared_by_title"
                            :error-message="errors.prepared_by_title"
                            required
                        />
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <app-form-group
                            :label="$t('payrun')"
                            type="select"
                            :list="payrunOptions"
                            v-model="form.payrun_id"
                            :error-message="errors.payrun_id"
                            required
                        />
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <app-form-group
                            :label="$t('notes')"
                            type="textarea"
                            :placeholder="$t('enter_notes')"
                            v-model="form.notes"
                            :error-message="errors.notes"
                        />
                    </div>
                    <div class="col-md-6">
                        <app-form-group
                            :label="$t('remarks')"
                            type="textarea"
                            :placeholder="$t('enter_remarks')"
                            v-model="form.remarks"
                            :error-message="errors.remarks"
                        />
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <app-submit-button
                            :loading="loading"
                            :label="isEdit ? $t('update') : $t('create')"
                        />
                    </div>
                </div>
            </form>
        </app-modal>

        <!-- Confirmation Modal -->
        <app-confirmation-modal
            v-if="confirmationModalActive"
            :loading="loading"
            :message="promptMessage"
            :modal-class="promptClass"
            :icon="promptIcon"
            modal-id="app-confirmation-modal"
            @confirmed="confirmed"
            @cancelled="confirmationModalActive = false"
            :self-close="false"
        />
    </div>
</template>

<script>
import {axiosGet, axiosPost, axiosPatch, axiosDelete, urlGenerator} from "../../../../common/Helper/AxiosHelper";
import {TRANSMITTAL} from "../../../Config/ApiUrl";

export default {
    name: "PayrollTransmittal",
    data() {
        return {
            isModalActive: false,
            loading: false,
            isEdit: false,
            selectedTransmittal: null,
            confirmationModalActive: false,
            actionType: '',
            promptMessage: '',
            promptClass: '',
            promptIcon: '',
            payrunOptions: [],
            departmentOptions: [],
            form: {
                title: '',
                department_id: '',
                payrun_id: '',
                payroll_period: '',
                reference_number: '',
                notes: '',
                remarks: '',
                period_from: '',
                period_to: '',
                prepared_by: '',
                prepared_by_title: ''
            },
            errors: {},
            options: {
                name: this.$t('transmittal'),
                url: TRANSMITTAL,
                showHeader: true,
                columns: [
                    {
                        title: this.$t('reference_number'),
                        type: 'text',
                        key: 'reference_number',
                        isVisible: true
                    },
                    {
                        title: this.$t('department'),
                        type: 'object',
                        key: 'department',
                        isVisible: true,
                        modifier: (department) => department?.name || '-'
                    },
                    {
                        title: this.$t('payroll_period'),
                        type: 'text',
                        key: 'payroll_period',
                        isVisible: true
                    },
                    {
                        title: this.$t('payrun'),
                        type: 'object',
                        key: 'payrun',
                        isVisible: true,
                        modifier: (payrun) => payrun?.name || '-'
                    },
                    {
                        title: this.$t('generated_by'),
                        type: 'object',
                        key: 'generated_by',
                        isVisible: true,
                        modifier: (user) => user?.full_name || '-'
                    },
                    {
                        title: this.$t('status'),
                        type: 'component',
                        key: 'status',
                        isVisible: true,
                        componentName: 'app-status'
                    },
                    {
                        title: this.$t('actions'),
                        type: 'action',
                        key: 'invoice',
                        isVisible: true
                    },
                ],
                filters: [
                    {
                        title: this.$t('created_date'),
                        type: 'range-picker',
                        key: 'date',
                        option: ['today', 'thisMonth', 'last7Days', 'thisYear']
                    }
                ],
                paginationType: "pagination",
                responsive: true,
                rowLimit: 10,
                showAction: true,
                orderBy: 'desc',
                actionType: "dropdown",
                actions: [
                    {
                        title: this.$t('view'),
                        icon: 'eye',
                        type: 'modal',
                        component: 'app-transmittal-view-modal',
                        modalId: 'transmittal-view-modal',
                        url: TRANSMITTAL,
                        name: 'view',
                        modifier: () => this.$can('view_payroll_transmittal')
                    },
                    {
                        title: this.$t('edit'),
                        icon: 'edit',
                        type: 'page',
                        name: 'edit',
                        modifier: () => this.$can('update_payroll_transmittal')
                    },
                    {
                        title: this.$t('export_pdf'),
                        icon: 'file-text',
                        type: 'none',
                        name: 'export_pdf',
                        modifier: () => this.$can('export_payroll_transmittal')
                    },
                    {
                        title: this.$t('export_csv'),
                        icon: 'download',
                        type: 'none',
                        name: 'export_csv',
                        modifier: () => this.$can('export_payroll_transmittal')
                    },
                    {
                        title: this.$t('delete'),
                        icon: 'trash-2',
                        type: 'modal',
                        component: 'app-confirmation-modal',
                        modalId: 'app-confirmation-modal',
                        url: TRANSMITTAL,
                        name: 'delete',
                        modifier: () => this.$can('delete_payroll_transmittal')
                    }
                ]
            }
        }
    },
    mounted() {
        this.getPayruns();
        this.getDepartments();
    },
    methods: {

        openModal() {
            this.isModalActive = true;
            this.isEdit = false;
            this.resetForm();
        },
        closeModal() {
            this.isModalActive = false;
            this.isEdit = false;
            this.selectedTransmittal = null;
            this.resetForm();
            this.errors = {};
        },
        resetForm() {
            this.form = {
                title: '',
                department_id: '',
                payrun_id: '',
                payroll_period: '',
                reference_number: '',
                notes: '',
                remarks: '',
                period_from: '',
                period_to: '',
                prepared_by: '',
                prepared_by_title: ''
            };
        },
        getDepartments() {
            axiosGet(`${TRANSMITTAL}/departments`).then(({data}) => {
                this.departmentOptions = data.map(department => ({
                    id: department.id,
                    value: department.name
                }));
            }).catch(error => {
                this.$toastr.e(error.response?.data?.message || 'Error loading departments');
            });
        },
        getPayruns() {
            axiosGet(`${TRANSMITTAL}/payruns`).then(({data}) => {
                this.payrunOptions = data.map(payrun => ({
                    id: payrun.id,
                    value: payrun.name + ' (' + payrun.uid + ')'
                }));
            }).catch(error => {
                this.$toastr.e(error.response?.data?.message || 'Error loading payruns');
            });
        },
        submitForm() {
            this.loading = true;
            this.errors = {};

            const request = this.isEdit
                ? axiosPatch(`${TRANSMITTAL}/${this.selectedTransmittal.id}`, this.form)
                : axiosPost(TRANSMITTAL, this.form);

            request.then(({data}) => {
                this.$toastr.s(data.message);
                this.closeModal();
                this.$hub.$emit('reload-transmittal-table');
            }).catch(error => {
                if (error.response?.status === 422) {
                    this.errors = error.response.data.errors;
                } else {
                    this.$toastr.e(error.response?.data?.message || 'An error occurred');
                }
            }).finally(() => {
                this.loading = false;
            });
        },
        triggerActions(row, action, active) {
            this.selectedTransmittal = row;

            if (action.name === 'edit') {
                this.editTransmittal(row);
            } else if (action.name === 'delete') {
                this.confirmDelete(row);
            } else if (action.name === 'export_pdf') {
                this.exportPdf(row);
            } else if (action.name === 'export_csv') {
                this.exportCsv(row);
            }
        },
        editTransmittal(transmittal) {
            this.isEdit = true;
            this.isModalActive = true;
            this.form = {
                title: transmittal.metadata?.title || '',
                department_id: transmittal.department_id,
                payrun_id: transmittal.payrun_id,
                payroll_period: transmittal.payroll_period,
                reference_number: transmittal.reference_number || '',
                notes: transmittal.notes || '',
                remarks: transmittal.remarks || '',
                period_from: transmittal.metadata?.period_from || '',
                period_to: transmittal.metadata?.period_to || '',
                prepared_by: transmittal.metadata?.prepared_by || '',
                prepared_by_title: transmittal.metadata?.prepared_by_title || ''
            };
        },
        confirmDelete(transmittal) {
            this.actionType = 'delete';
            this.promptMessage = this.$t('are_you_sure_you_want_to_delete_this_transmittal');
            this.promptClass = 'warning';
            this.promptIcon = 'trash-2';
            this.confirmationModalActive = true;
        },
        confirmed() {
            if (this.actionType === 'delete') {
                this.deleteTransmittal();
            }
        },
        deleteTransmittal() {
            this.loading = true;
            axiosDelete(`${TRANSMITTAL}/${this.selectedTransmittal.id}`)
                .then(({data}) => {
                    this.$toastr.s(data.message);
                    this.$hub.$emit('reload-transmittal-table');
                })
                .catch(error => {
                    this.$toastr.e(error.response?.data?.message || 'Error deleting transmittal');
                })
                .finally(() => {
                    this.loading = false;
                    this.confirmationModalActive = false;
                });
        },
        exportPdf(transmittal) {
            window.open(`${TRANSMITTAL}/${transmittal.id}/export-pdf`, '_blank');
        },
        exportCsv(transmittal) {
            window.open(`${TRANSMITTAL}/${transmittal.id}/export-csv`, '_blank');
        }
    }

<style scoped>
.content-wrapper {
    padding: 20px;
}
</style>
