<?php
return array_merge([
    // Responses
    'created_response' => ':name has been created successfully',
    'updated_response' => ':name has been updated successfully',
    'deleted_response' => ':name has been deleted successfully',
    'failed_response' => 'Something went wrong',
    'notified' => ':name has been notified successfully',
    'duplicated_response' => ':name has been duplicated successfully',
    'status_updated_response' => ':name status has been changed to :status',
    'action_not_allowed' => 'You are not allowed for this action',
    'cant_delete_own_account' => 'You can\'t delete your own account',
    'attached_response' => ':name has been attached successfully',
    'detached_response' => ':name has been detached successfully',
    'default_delete' => 'You can not delete the default :name .',
    'default_update' => 'You can not update the default :name',
    'old_password_is_in_correct' => 'Old password is incorrect',
    'attach_log' => 'New :pivot attached to :model',
    'detach_log' => ':pivot detached from :model',
    'status_log' => ':model has been :status',
    'incorrect_user_password' => 'Incorrect user or password',
    'invite_user_response' => 'User has been invited successfully',
    'invalid_token' => 'The token is Invalid',
    'user_account_confirmed' => 'Your account has been confirmed successfully',
    'user_invited_to_join' => 'An user has been invited to join',
    'user_confirm_joining' => 'User confirmed his joining',
    'log_description_message' => ':model has been :event',
    'password_reset_mail_has_been_sent_successfully' => 'We sent an email containing password reset link to your email address. Please check it',
    'no_user_found_on_that_email' => 'No user found of that email address.',
    'password_has_been_reset_successfully' => 'Your password has been reset successfully',
    'password_reset_link' => 'Password Reset Link',
    'resource_not_found' => 'The :resource you are looking for is not found.',
    'created' => 'Created',
    'deleted' => 'Deleted',
    'updated' => 'updated',
    'resource' => 'resource',
    'this_resource_already_referenced_message' => 'this content have relations with others table',

    '0' => 'ZERO (0)',
    // HTTP Responses
    '200' => 'Success',
    '2' => 'TWO (2)',
    '400' => 'Bad Request',
    '401' => 'Unauthorized',
    '403' => 'Forbidden',
    '404' => 'Not Found',
    '413' => 'Payload too large',
    '414' => 'URI Too long',
    '415' => 'Unsupported Media Type',
    '426' => 'Upgrade Required',
    '429' => 'Too Many Requests',

    // Features

    // Custom Field Builder
    'custom_field' => 'Custom Field',
    'custom_fields' => 'Custom Fields',
    'custom_field_type' => 'Custom field type',
    'validation.required' => ':attribute',
    'audience_type' => 'audience(roles/users)',
    'the_notification_channel_field_is_required' => 'The notification channel field is required.',

    // Fields
    'text' => 'Textbox',
    'textarea' => 'Textarea',
    'checkbox' => 'Checkbox',
    'radio_button' => 'Radio Button',
    'select' => 'Select',
    'multi_select' => 'Multi Select',
    'radio' => 'Radio',
    'did_not_match_anything' => "Didn't match anything",
    'enter_to_add_new' => 'Enter to add new',
    'no_options_found' => 'No options found',
    'pick_a_color' => 'Pick a color',
    'job_desk' => 'Job Desk',

    // Notification event
    'notification_events' => 'Notification Events',
    'notification_event_name' => ':name :action',
    'notification_created' => 'created',
    'notification_updated' => 'updated',
    'notification_deleted' => 'deleted',
    'notification_user' => 'user',
    'notification_reset' => 'reset',
    'notification_invitation' => 'invitation',
    'notification_create' => 'create',
    'notification_joined' => 'joined',
    'notification_activated' => 'activated',
    'notification_deactivated' => 'deactivated',
    'notification_' => ' ',
    'password-reset' => 'Password reset',
    'notification_canceled' => 'canceled',
    'notification_requested' => 'requested',
    'notification_rejected' => 'rejected',
    'notification_approved' => 'approved',
    'notification_bypassed' => 'bypassed',
    'notification_assigned' => 'assigned',
    'notification_terminated' => 'terminated',
    'notification_termination' => 'termination',
    'notification_increment' => 'increment',
    'notification_generate' => 'generate',
    'employee_password' => 'Employee password',
    'notification_failed' => 'Failed',
    'notification_complete' => 'Complete',

    // Notifications
    'notification' => 'Notification',
    'notify_by_email' => 'Notify by Email',
    'notify_by_sms' => 'Notify by SMS',
    'notification_settings' => 'Notification settings',
    'notification_template' => 'Notification template',
    'mark_all_as_read' => 'Mark all as read',
    'see_all' => 'See all',
    'notification_has_cleared_from_list' => 'Notification has cleared from list.',

    // Labels
    'age' => 'Age',
    'show' => 'Show',
    'hide' => 'Hide',
    'user' => 'User',
    'brand' => 'Brand',
    'status' => 'Status',
    'name' => 'Name',
    'email' => 'Email',
    'mail' => 'Mail',
    'value' => 'Value',
    'type' => 'Type',
    'database' => 'System',
    'sms' => 'SMS',
    'users' => 'Users',
    'roles' => 'Roles',
    'role' => 'Role',
    'permissions' => 'Permissions',
    'permission' => 'Permission',
    'settings' => 'Settings',
    'password' => 'Password',
    'show_password' => 'Show password',
    'hide_password' => 'Hide password',
    'allowed' => 'allowed',
    'profile_picture' => 'Profile picture',
    'delivery_settings' => 'Email settings',
    'brand_settings' => 'Brand settings',
    'privacy_settings' => 'Privacy settings',
    'corn_job' => 'Corn job',
    'brand_group' => 'Brand group',
    'template' => 'Template',
    'profile' => 'Profile',
    'log' => 'Log',
    'invite' => 'Invite',
    'dashboard' => 'Dashboard',
    'log_out' => 'Log out',
    'my_profile' => 'My profile',
    'notifications' => 'Notifications',
    'contents' => 'Contents',
    'update' => 'Update',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'cancel' => 'Cancel',
    'save' => 'Save',
    'personal_info' => 'Personal Details',
    'first_name' => 'First name',
    'last_name' => 'Last name',
    'gender' => 'Gender',
    'date_of_birth' => 'Date of birth',
    'contact' => 'Contact',
    'address' => 'Address',
    'not_added_yet' => 'Not added yet',
    'male' => 'Male',
    'female' => 'Female',
    'password_requirements_message' => 'The password should contain one upper case, one lower case, one special character, and numbers. It should be a minimum of 8 characters.',
    'new' => 'New',
    'old' => 'Old',
    'confirm' => 'Confirm',
    'old_password' => 'Old password',
    'notification_setting' => 'Notification setting',
    'confirm_your_account' => 'Confirm your account.',
    'reason_note' => 'Reason note',
    'add_reason_note_here' => 'Add reason note here',
    'add_response_note_here' => 'Add response note here',
    'attachments' => 'Attachments',
    'attachment' => 'Attachment',
    'enter_date' => 'Enter date',
    'enter_start_date' => 'Enter start date',
    'enter_start_time' => 'Enter start time',
    'enter_end_date' => 'Enter end date',
    'enter_end_time' => 'Enter end time',
    'date_and_time' => 'Date & time',
    'activity' => 'Activity',
    'view_as_employee' => 'View As Employee',
    'view_as_admin' => 'View As Admin',
    'view_as_manager' => 'View As Manager',
    'change_password' => 'Change Password',

    // Settings
    'app_name' => 'App Name',
    'brand_name' => 'Brand Name',

    // Status
    'status_active' => 'Active',
    'status_pending' => 'Pending',
    'status_deleted' => 'Deleted',
    'status_processing' => 'Processing',
    'status_sent' => 'Sent',
    'status_draft' => 'Draft',
    'status_regular' => 'Regular',
    'status_auto' => 'Auto',
    'status_dynamic' => 'Dynamic',
    'status_imported' => 'Imported',
    'status_black-listed' => 'Black listed',
    'status_inactive' => 'Inactive',
    'status_invited' => 'Invited',
    'status_approve' => 'Approved',
    'status_approved' => 'Approved',
    'status_reject' => 'Rejected',
    'status_rejected' => 'Rejected',
    'status_canceled' => 'Canceled',
    'status_cancel' => 'Canceled',
    'status_log' => 'Log',
    'status_bypassed' => 'Bypassed',
    'status_generated' => 'Generated',
    'status_completed' => 'Completed',
    'you_are_going_to_approve_this_leave_request' => 'You are going to approve this leave request',
    'you_are_going_to_reject_this_leave_request' => 'You are going to reject this leave request',
    'you_are_going_to_cancel_this_leave_request' => 'You are going to cancel this leave request',
    'you_have_been_terminated' => 'You have been terminated!',

    // Permissions
    'manage_dashboard' => 'Can manage app dashboard',
    'view_users' => 'Can view list of user',
    'create_users' => 'Can create an user',
    'update_users' => 'Can update an user',
    'delete_users' => 'Can delete an user',
    'view_brands' => 'Can view list of brand',
    'create_brands' => 'Can create brand',
    'update_brands' => 'Can update brand',
    'delete_brands' => 'Can delete brand',
    'user_list_brands' => 'Can view users of a brand',
    'attach_users_brands' => 'Can attach user to brand',
    'detach_users_brands' => 'Can detach user from a brand',
    'brand_list_users' => 'Can view brands of a user',
    'manage_brand_dashboard' => 'Can view brand dashboard',
    'update_brand_privacy_settings' => 'Can update brand privacy from app',
    'view_brand_privacy_settings' => 'Can view brand privacy from app',
    'view_roles' => 'Can view list of role',
    'create_roles' => 'Can create role',
    'update_roles' => 'Can update role',
    'delete_roles' => 'Can delete role',
    'view_settings' => 'Can view list of setting',
    'update_settings' => 'Can update setting',
    'view_permission' => 'Can view list of permission',
    'view_custom_fields' => 'Can view list of custom field',
    'create_custom_fields' => 'Can create custom field',
    'update_custom_fields' => 'Can update custom field',
    'delete_custom_fields' => 'Can delete custom field',
    'attach_roles_users' => 'Can attach roles to users',
    'detach_roles_users' => 'Can detach roles from users',
    'attach_permissions_roles' => 'Can attach permissions to role',
    'detach_permissions_roles' => 'Can detach permissions from role',
    'change_settings_users' => 'Can change own settings',
    'settings_list_users' => 'Can view settings list',
    'change_password_users' => 'Can change user password',
    'change_profile_picture_users' => 'Can change profile picture',
    'update_delivery_settings' => 'Can update email settings',
    'update_corn_job_settings' => 'Can update corn job settings',
    'view_corn_job_settings' => 'Can view corn job settings',
    'view_delivery_settings' => 'Can view email settings',
    'view_brand_delivery_settings' => 'Can view brand email settings',
    'view_notification_settings' => 'Can view notification settings',
    'update_notification_settings' => 'Can update notification settings',
    'create_brand_groups' => 'Can create brand group',
    'view_brand_groups' => 'Can view brand group',
    'update_brand_groups' => 'Can update brand group',
    'delete_brand_groups' => 'Can delete brand group',
    'attach_brand_brand_groups' => 'Can attach brand to brand group',
    'detach_brand_brand_groups' => 'Can detach brand from brand group',
    'view_brands_brand_groups' => 'Can view brands of a brand group',
    'view_notification_templates' => 'Can view notification templates',
    'create_notification_templates' => 'Can create notification templates',
    'update_notification_templates' => 'Can update notification templates',
    'delete_notification_templates' => 'Can delete notification templates',
    'attach_templates_notification_events' => 'Can attach templates to notification event',
    'detach_templates_notification_events' => 'Can detach templates to notification event',
    'view_activity_logs' => 'Can view activity log',
    'view_templates' => 'Can view templates',
    'create_templates' => 'Can create templates',
    'update_templates' => 'Can update templates',
    'delete_templates' => 'Can delete templates',
    'invite_user' => 'Can invite user',
    'create_employment_statuses' => 'Can create employment status',
    'view_employment_statuses' => 'Can view employment status',
    'update_employment_statuses' => 'Can update employment status',
    'delete_employment_statuses' => 'Can delete employment status',
    'create_designations' => 'Can create designation',
    'view_designations' => 'Can view designation',
    'update_designations' => 'Can update designation',
    'delete_designations' => 'Can delete designation',
    'cancel_user_invitation' => 'Can cancel user invitation',
    'update_departments_status' => 'Can update department status',
    'view_organization_chart' => 'Can view organization chart',
    'create_working_shifts' => 'Can create working shift',
    'view_working_shifts' => 'Can view working shift',
    'update_working_shifts' => 'Can update working shift',
    'delete_working_shifts' => 'Can delete working shift',
    'add_employees_to_working_shift' => 'Can add employees to work shift',
    'view_all_departments_holidays' => 'Can view all departments holidays',
    'create_holidays' => 'Can create holiday',
    'update_holidays' => 'Can update holidays',
    'view_holidays' => 'Can view holidays',
    'delete_holidays' => 'Can delete holidays',
    'view_employees' => 'Can view employees',
    'update_employees' => 'Can update employees',
    'delete_employees' => 'Can delete employees',
    'invite_employees' => 'Can invite employees',
    'add_employees' => 'Can add employees',
    'terminate_employees' => 'Can terminate employees',
    'rejoin_employees' => 'Can re-join employees',
    'add_user_to_employees' => 'Can add user to employee',
    'remove_user_from_employees' => 'Can remove user from employees',
    'cancel_employee_invitation' => 'Can cancel employee invitations',
    'view_employee_address' => 'Can view employee address',
    'update_employee_address' => 'Can update employee address',
    'delete_employee_address' => 'Can delete employee address',
    'view_employee_emergency_contacts' => 'Can view employee emergency contacts',
    'create_employee_emergency_contacts' => 'Can create employee emergency contacts',
    'update_employee_emergency_contacts' => 'Can update employee emergency contacts',
    'delete_employee_emergency_contacts' => 'Can delete employee emergency contacts',
    'view_employee_social_links' => 'Can view employee social links',
    'update_employee_social_links' => 'Can update employee social links',
    'view_attendances_details' => 'Can view attendance details',
    'move_department_employees' => 'Can move department employees',
    'view_department_user' => 'Can view department users',
    'update_employees_profile' => 'Can update employee personal details',
    'view_attendance_requests' => 'Can view attendance request',
    'create_attendances' => 'Can add employees attendance',
    'update_attendance_notes' => 'Can update own attendance notes',
    'update_attendance_details_status' => 'Can update attendance status',
    'send_attendance_request' => 'Can send attendance request',
    'update_attendance_status' => 'Can update attendance status',
    'create_leave_periods' => 'Can create leave period',
    'view_leave_periods' => 'Can view leave period',
    'update_leave_periods' => 'Can update leave period',
    'delete_leave_periods' => 'Can delete leave period',
    'view_attendance_summary' => 'Can view attendance summary',
    'attendance_change_log' => 'Can view change log',
    'update_attendances' => 'Can update attendance',
    'attendances_daily_log' => 'Can view attendance daily log',
    'update_attendance_settings' => 'Can update attendance settings',
    'view_attendance_settings' => 'Can view attendance settings',
    'view_all_attendance' => 'Can view all attendance (If you set this permission to true any user with this role can access all attendances from any list.)',
    'update_employee_leave_amount' => 'Can update employee leave amount',
    'manage_leave_allowance_policy' => 'Can manage leave allowance policy',
    'view_leave_calendar' => 'Can view leave calendar',
    'update_employee_job_history' => 'Can update employee job history',
    'view_job_history' => 'Can view job history',
    'create_leave_request' => 'Can send leave request',
    'approve_attendance' => 'can approve attendance',
    'reject_attendance' => 'Can reject attendance',
    'cancel_attendance' => 'Can cancel attendance request',
    'view_leave_status_summary' => 'Can view leave status',
    'view_leave_data_table' => 'Can view leave data table',
    'update_leave_settings' => 'Can update leave settings',
    'view_leave_status' => 'Can view leave status',
    'update_monthly_earning' => 'Can update monthly earning',

    'view_leave_settings' => 'Can view leave settings',
    'manage_cancel_leave' => 'Can cancel own leave request',
    'manage_bypass_leave' => 'Can bypass leave request',
    'manage_approve_leave' => 'Can approve leave request',
    'manage_reject_leave' => 'Can reject leave request',
    'create_leave_types' => 'Can create leave types',
    'view_leave_types' => 'Can view leave types',
    'update_leave_types' => 'Can update leave types',
    'delete_leave_types' => 'Can delete leave types',
    'view_leave_summaries' => 'Can view leave summaries',
    'view_leave_requests' => 'Can view leave requests',
    'update_leave_notes' => 'Can update own leave notes',
    'assign_leaves' => 'Can assign leaves',
    'view_all_leaves' => 'Can view all leaves',

    'date_format' => 'date format',
    'time_format' => 'time format',
    'decimal_separator' => 'decimal separator',
    'thousand_separator' => 'thousand separator',
    'number_of_decimal' => 'number of decimal',
    'currency_position' => 'currency position',

    'check_for_updates' => 'Can check for updates',
    'update_app' => 'Can update app',

    'view_salary' => 'Can view salary',
    'update_salary' => 'Can update salary',
    'view_payroll_settings' => 'Can view payroll settings',

    //Payroll Permissions
    'view_beneficiaries' => 'Can view beneficiary badges',
    'edit_beneficiaries' => 'Can edit beneficiary badges',
    'update_beneficiaries' => 'Can update beneficiary badges',
    'delete_beneficiaries' => 'Can delete beneficiary badges',
    'create_beneficiaries' => 'Can create beneficiary badges',
    'view_payslips' => 'Can view payslip',
    'delete_payslip' => 'Can delete payslip',
    'update_payslip' => 'Can update payslip',
    'view_payslip_pdf' => 'Can view and download payslip pdf',
    'send_bulk_payslip' => 'Can send bulk payslip',
    'unpaid_leave_deduction' => 'Unpaid leave deduction',
    'overtime_earning' => 'Overtime earning',

    'run_default_payrun' => 'Can run default payrun',
    'run_manual_payrun' => 'Can run manual payrun',
    'view_payruns' => 'Can view payruns',
    'update_payruns' => 'Can update payruns',
    'delete_payruns' => 'Can delete payruns',
    'send_payrun_payslips' => 'Can send payrun payslips',
    'view_payroll_summery' => 'Can view payroll summery',
    'view_default_payrun' => 'Can view default payrun',
    'update_payrun_audience' => 'Can update payrun audience setting',
    'update_payrun_beneficiary' => 'Can update payrun beneficiary setting',
    'update_payrun_period' => 'Can update payrun period setting',
    'send_individual_payslip' => 'Can send individual payslip',
    'edit_payslip' => 'Can edit payslip',
    'manage_payslip_confliction' => 'Can manage payslip confliction',
    'update_employee_payrun' => 'Can update employee payrun',
    'update_employee_beneficiary' => 'Can update employee beneficiary',

    //Payroll New Permissions
    'view_payroll_transmittal' => 'Can view transmittal',
    'create_payroll_transmittal' => 'Can create transmittal',
    'view_payroll_for_signature' => 'Can view for signature',
    'create_payroll_for_signature' => 'Can create for signature',
    'view_payroll_consolidation' => 'Can view consolidation',
    'create_payroll_consolidation' => 'Can create consolidation',

    //import
    'import_employees' => 'Can import employees',
    'import_attendances' => 'Can import attendances',

    //Export
    'export_attendance_summery' => 'Can export attendance summery',
    'export_leave_summery' => 'Can export leave summery',
    'export_all_attendance_summery' => 'Can export all attendance summery',
    'export_assets' => 'Can export assets',
    'export_payslips' => 'Can export payslips',
    'export_attendance_daily_log' => 'Can export attendance daily log',
    'export_attendance' => 'Export attendance',
    'is_department_manager' => 'Is department manager',

    // company asset types
    'create_company_asset_types' => 'Can create company asset types',
    'view_company_asset_types' => 'Can view company asset types',
    'update_company_asset_types' => 'Can update company asset types',
    'delete_company_asset_types' => 'Can delete company asset types',
    // company assets
    'create_company_assets' => 'Can create company assets',
    'view_company_assets' => 'Can view company assets',
    'update_company_assets' => 'Can update company assets',
    'delete_company_assets' => 'Can delete company assets',


    // Add and all labels
    'field_label' => '{subject} {key}',
    'textarea_placeholder' => 'Add {name} here',

    'all_feature_name' => 'All {name}',
    'add_feature_name' => 'Add {name}',
    'edit_feature_name' => 'Edit {name}',
    'copy_feature_name' => 'Copy {name}',
    'field_title' => '{subject} {infix} {title}',
    'place_holder' => 'Enter {subject} {type}',
    'title' => 'Title',
    'notification_form' => 'Notification form',
    'time' => 'Time',
    'event' => 'Event',
    'templates' => 'Templates',
    'email_setup' => 'Email setup',
    'smtp' => 'SMTP',
    'amazon_ses' => 'Amazon SES',
    'mailgun' => 'Mailgun',
    'mandrill' => 'Mandrill',
    'postmark' => 'Postmark',
    'sparkpost' => 'Sparkpost',
    'sendmail' => 'Sendmail',
    'path' => 'Path',
    ' ' => 'Space',
    'provider' => 'Provider',
    'secret' => 'Secret',
    'domain_name' => 'Domain name',
    'api_key' => 'API key',
    'from' => 'From',
    'host' => 'Host',
    'port' => 'Port',
    'encryption' => 'Encryption',
    'api_region' => 'API region',
    'access_key_id' => 'Access key',
    'secret_access_key' => 'Secret access key',
    'token' => 'Token',
    'user_invitation' => 'User invitation',
    'paid' => 'Paid',
    'unpaid' => 'Unpaid',
    'un_paid' => 'Un paid',
    'amount' => 'Amount',
    'special_percentage' => 'Special percentage',
    'special' => 'Special',
    'start_date' => 'Start date',
    'end_date' => 'End date',
    'smtp_encryption' => 'SMTP encryption',
    'smtp_password' => 'SMTP password',
    'smtp_user_name' => 'SMTP username',
    'smtp_port' => 'SMTP port',
    'smtp_host' => 'SMTP host',
    'default_mail' => 'Default mail',
    'from_name' => 'From name',

    // Language
    'en' => 'English',

    // Date Format
    'dd-mm-yyyy' => 'DD-MM-YYYY',
    'd-m-Y' => 'DD-MM-YYYY',
    'm-d-Y' => 'MM-DD-YYYY',
    'Y-m-d' => 'YYYY-MM-DD',
    'm/d/Y' => 'MM/DD/YYYY',
    'd/m/Y' => 'DD/MM/YYYY',
    'Y/m/d' => 'YYYY/MM/DD',
    'm.d.Y' => 'MM.DD.YYYY',
    'd.m.Y' => 'DD.MM.YYYY',
    'Y.m.d' => 'YYYY.MM.DD',

    // Time Format
    'h' => '12 HOURS',
    'H' => '24 HOURS',

    // Decimal and Thousand Separator
    '.' => 'DOT(.)',
    ',' => 'COMMA(,)',

    // Currency Positions
    'prefix_only' => '$1,100.00',
    'prefix_with_space' => '$ 1,100.00',
    'suffix_only' => '1,100.00$',
    'suffix_with_space' => '1,100.00 $',

    'format_prefix_only' => '{symbol}{amount}',
    'format_prefix_with_space' => '{symbol} {amount}',
    'format_suffix_only' => '{amount}{symbol}',
    'format_suffix_with_space' => '{amount} {symbol}',

    'like_prefix_only' => ':symbol:amount',
    'like_prefix_with_space' => ':symbol :amount',
    'like_suffix_only' => ':amount:symbol',
    'like_suffix_with_space' => ':amount :symbol',

    // Validation
    "is_required" => "is required",
    "and" => "and",
    "this_field_is_required" => "This field is required",
    "this_field_is_invalid" => "This field is invalid",
    "this_field_is_not_alphanumeric" => "This field is not alphanumeric",
    "passwords_are_not_matched" => "Passwords are not matched",
    "please_enter_a_strong_password" => "Please enter a strong password.",
    "are_not_match" => "are not match",
    "can_not_before" => "can not before",
    "is_invalid" => "is invalid",
    "minimum_length_is" => "Minimum length is",
    "maximum_length_is" => "Maximum length is",
    "maximum_number_is" => "Maximum number is",
    "minimum_number_is" => "Minimum number is",
    "is_not_alphanumeric" => "is not alphanumeric",
    "not_found" => "Not found",
    "you_are_going_to_cancel_an_invitation" => "You are going to cancel an invitation.",
    'hands_up' => 'Hand\'s Up!',
    'you_are_going_to_delete_message' => 'You are going to delete {resource}',

    // Btn
    "action" => "Action",
    "load_more" => "Load more",
    "apply" => "Apply",
    "clear" => "Clear",
    "close" => "Close",
    "yes" => "Yes",
    "no" => "No",
    "more" => "more",
    "actions" => "Actions",
    'context' => 'Context',
    'choose_multi_field' => 'Choose {field}',
    "choose_field" => "Choose a {field}",
    "select_field" => 'Select a {field}',
    'options' => 'Options',
    'cancel_invitation' => 'Cancel Invitation',
    'remove_link' => 'Remove link',

    // Multi select component
    "add" => "Add",

    // Datatable
    "items_showing_per_page" => 'Items showing per page',
    "items_selected" => "items selected",
    "select_all" => "Select all",
    "clear_selection" => "Clear selection",
    "showing" => "Showing",
    "to" => "to",
    "items" => "items",
    "of" => "of",

    // Filters
    "filters" => "Filters",
    "minimum_rate" => "Minimum rate",
    "maximum_rate" => "Maximum rate",
    "want_to_manage_datatable" => "Want to manage datatable?",
    "please_drag_and_drop_your_column_to_reorder_your_table_and_enable_see_option_as_you_want" => "Please drag and drop your column to reorder your table and enable see option as you want.",
    "manage_columns" => "Manage Columns",
    "search" => "Search",
    "today" => "Today",
    "date" => "Date",
    "select_an_option" => "Select an option",
    "clear_all_filters" => "Clear all filters",
    "sort_by" => "Sort by",
    "can_not_fetch_data" => "Can not fetch data",

    // Modal
    "are_you_sure" => "Are you sure?",
    "this_content_will_be_deleted_permanently" => "This content will be deleted permanently.",

    // Empty data
    "nothing_to_show_here" => "Nothing to show here",
    "thank_you" => "Thank you",
    "go_back_to_your_page" => "Go back to your page",
    "something_went_wrong" => "Something went wrong!",
    "empty_data_block_dummy_message" => "Please add a new entity or manage the data table to see the content here",

    // File upload
    "change_image" => "Change Image",
    "choose_file" => "Choose File",
    "drag_and_drop" => "Drag & Drop",
    "or" => "or",
    "browse" => "Browse",

    // No notification
    "no_notification_one" => "It's very much boring to do as usual stuff, let's have a party with some beer!",
    "no_notification_two" => "Are you hungry there? Please have good food and get back to work.",
    "no_notification_three" => "Rock & role time! Turn on your music and have some fun with your team.",
    "all_notifications" => "All Notifications",

    // Tooltip titles
    "collapse_sidebar" => "Collapse sidebar",
    "floating_sidebar" => "Floating sidebar",
    "full_sidebar" => "Full sidebar",
    "light_mood" => "Light mood",
    "dark_mood" => "Dark mood",
    "fullscreen" => "Fullscreen",
    "exit_fullscreen" => "Exit fullscreen",

    // Login
    "hi" => "Hi",
    "copyright" => "Copyright",
    "login_to_your_dashboard" => "Login to your dashboard",
    "login" => "Login",
    "your" => "Your",
    "forget" => "Forget {subject}",
    "reset_password" => "Reset Password",
    "back_to" => "Back to {destination}",
    "request" => "Request",
    "change" => "Change",
    "password_must_contains_things" => "Password must contains number, lowercase, uppercase and special character",
    "the_user_you_are_looking_for_is_not_found" => "The user you are looking for is not found.",

    // Tenant Preview Card
    "invited_by" => "Invited by",
    "short_name" => "Short name",
    "group" => "Group",
    "go_to_dashboard" => "Go To Dashboard",
    "user_invitation_canceled_successfully" => "User invitation canceled successfully.",

    // Time - picker input
    "am" => "AM",
    "pm" => "PM",

    // Sidebar
    'users_roles' => 'Users & Roles',
    'no_delivery_settings_found' => 'No email settings found.',
    'no_delivery_settings_warning' => 'For sending invitation you must set up email settings. Click {location} to add email settings.',
    'no_delivery_settings_warning_none' => 'For sending invitation you must set up email settings and setup cron job on your hosting server.',
    'no_delivery_settings_warning_on_import' => 'To import employees you must set up email settings. Click {location} to add email settings.',
    'no_delivery_settings_warning_none_on_import' => 'To import employees you must set up email settings and setup cron job on your hosting server.',
    'cron_job_settings_warning' => 'Please make sure you setup the cron job on your hosted server as instructed on the {documentation} for sending emails.',


    // Buttons
    'done' => 'Done',
    'manage' => 'Manage',
    'de_activate' => 'De-activate',
    'notification_invited' => 'invited',
    'app_settings' => 'App Settings',
    'administration' => 'Administration',
    'work_shifts' => 'Work Shifts',
    'departments' => 'Departments',
    'org_structure' => 'Org. Structure',
    'attendance_settings' => 'Attendance Settings',
    'all_attendances' => 'All Attendances',
    'attendance' => 'Attendance',
    'attendance_request' => 'Attendance Request',
    'summery' => 'Summary',
    'calendar' => 'Calendar',
    'leave' => 'Leave',
    'employee' => 'Employee',
    'all_employee' => 'Employee Directory',
    'designation' => 'Designation',
    'employment_status' => 'Employment Status',
    'department' => 'Department',
    'choose' => 'Choose',
    'class' => 'Class',
    'purple' => 'Purple',
    'success' => 'Success',
    'info' => 'Info',
    'warning' => 'Warning',
    'primary' => 'Primary',
    'danger' => 'Danger',
    'okay' => 'Okay',
    'this_will_be_the_badge_of_the_employee' => 'This will be the badge of the employee',
    'employees_today_entries' => 'The employee has {length} entries today',

    // Modal
    'wait_a_minute' => 'Wait a minute!',
    'popup_subtitle_for_redirect_notification_template' => 'We have some email & notification templates for you. Please have a look on these for customization.',

    // Department
    'create_departments' => 'Can create departments',
    'view_departments' => 'Can view departments',
    'update_departments' => 'Can update departments',
    'delete_departments' => 'Can delete departments',
    'attach_users_to_roles' => 'Can attach users to role',
    'parent_department' => 'Parent department',
    'location' => 'Location',
    'description' => 'Description',
    'no_of_user' => 'No. of Employees',
    'remember_me' => 'Remember me',
    'parent_already_exists_warning' => 'Most parent department already exist. If you want to add new department you have to add it under a department.',
    'move_response' => ':subject moved to :location successfully',
    'manager_role_not_found' => 'Manager role not found',
    'number' => 'Number',
    'cant_add_child_department_as_parent_department' => 'You can\'t add child department as parent department',

    // Holiday
    'holiday' => 'Holiday',
    'description_here' => 'description here',
    'created_date' => 'Created date',
    'repeats_annually' => 'Repeats annually',
    'available_for' => 'Available for',
    'time_period' => 'Time period',
    'all' => 'All',
    'available_for_as_default_to_all' => 'Available for (As default to all)',
    'you_can_set_the_holiday_only_for_specific_department_by_adding_them' => 'You can set the holiday only for specific department by adding them.',
    'you_are_going_to_delete_a_holiday' => 'You are going to delete a holiday.',
    'here' => 'here',
    'cant_update_holidays' => 'You can\'t update previous years expended holidays',
    'cant_update_repeated_holidays' => 'You can\'t update repeated holidays',
    'cant_add_holidays_past' => 'Holiday can not be added on past dates',
    'update_note_this_year_expended_holiday' => 'You can not update holidays that are already spent',

    // Working Shift
    'working_shift' => 'Working shift',
    'working_shifts' => 'Working shifts',
    'start_at' => 'Start at',
    'end_at' => 'End at',
    'is_default' => 'Is default',
    'start_time' => 'Start time',
    'end_time' => 'End time',
    'is_week_end' => 'Is week end',
    'sun' => 'Sunday',
    'mon' => 'Monday',
    'tue' => 'Tuesday',
    'wed' => 'Wednesday',
    'thu' => 'Thursday',
    'fri' => 'Friday',
    'sat' => 'Saturday',
    'add_employee' => 'Add employee',
    'work_shift_type' => 'Work shift type',
    'regular' => 'Regular',
    'scheduled' => 'Scheduled',
    'choose_a_working_shift_type' => 'Choose a working shift type',
    'set_regular_week' => 'Set Regular Week',
    'set_week_with_fixed_time' => 'Set week with fixed time',
    'set_scheduled_week' => 'Set Scheduled Week',
    'set_week_with_customized_time_and_without_time_and_without_time_it_will_be_weekend' => 'Set week with customized time and without time it will be weekend.',
    'select_your_weekend_days' => 'Select weekend day (off days)',
    'weekend' => 'Weekend',
    'you_dont_have_permissions' => 'You don\'t have permission to update',
    'view' => 'View',
    'scheduled_week' => 'Scheduled week time',
    'regular_week' => 'Regular week time',
    'working_shift_type' => 'Working Shift type',
    'weekend_days' => 'Weekend days',

    // Week
    'sunday' => 'Sunday',
    'monday' => 'Monday',
    'tuesday' => 'Tuesday',
    'wednesday' => 'Wednesday',
    'thursday' => 'Thursday',
    'friday' => 'Friday',
    'saturday' => 'Saturday',
    'work_shift' => 'Work Shift',
    'added_response' => ':subject has been added to :object successfully',

    // Employment status
    'preview' => 'Preview',
    'badge_color' => 'Badge color',
    'color_value' => 'Color value',

    // Employee
    'all_employees' => 'Employees Directory',
    'employee_id' => 'Employee ID',
    'joining_date' => 'Date Hired',
    'contact_number' => 'Contact number',
    'view_details' => 'View details',
    'terminate' => 'Terminate',
    're_joining' => 'Re-joining',
    'invite_employee_response' => 'Employee has been invited successfully.',
    'create_employee_response' => 'Employee has been added successfully.',
    'salary' => 'Salary',
    'not_yet_joined' => 'Not yet joined',
    'default_termination_reason_note' => 'This is note for termination reason.',
    'enter_employee_termination_reason' => 'Enter employee termination reason...',
    'enter_employee_rejoining_reason' => 'Enter employee rejoining reason...',
    'back_to_all_employees' => 'Back to all employees',
    'add_joining_date' => 'Add joining date',
    'rejoin' => 'Rejoin',
    'rejoin_employee' => 'Rejoin Employee',
    'edit_joining_date' => 'Edit joining date',
    'add_salary' => 'Add salary',
    'edit_salary' => 'Edit salary',
    'employee_salary' => 'Employee salary',

    // Employee Details
    'employee_details' => 'Employee Details',
    'personal_details' => 'Personal Details',
    'address_details' => 'Address Details',
    'permanent_address' => 'Permanent address',
    'current_address' => 'Current address',
    'emergency_contacts' => 'Emergency Contacts',
    'you_can_add_multiple_contacts' => 'You can add multiple contacts',
    'social_links' => 'Social Links',
    'job_history' => 'Job History',
    'salary_overview' => 'Salary Overview',
    'active_log' => 'Active Log',
    'termination_reason' => 'Termination reason',
    'about_me' => 'About me',
    'user_added_to_employee' => 'User has been added to employee list successfully.',
    'user_removed_from_employee' => 'User has been removed from employee list successfully',
    'add_attendance' => 'Add attendance',
    'request_attendance' => 'Request attendance',
    'salary_and_reviews' => 'Salary and reviews',
    'you_are_going_to_terminate_an_employee' => 'You are going to terminate an employee.',
    'you_are_permitting_an_employee_for_re_joining' => 'You are permitting an employee for re-joining.',
    'you_are_going_to_remove_an_employee_from_list' => 'You are going to remove an employee from employee list.',
    'confirm_employee_remove' => 'Confirm Employee Remove',
    'confirm_employee_rejoining' => 'Confirm Employee Rejoining',
    'all_selected_employee_will_be_removed_from_employee_list' => 'All selected employee will be removed from employee list!',
    'rejoining' => 'Rejoining',
    'employee_invitation_canceled_successfully' => 'Employee invitation has been canceled successfully.',
    "birthday" => 'Birthday',
    "area" => 'Area',
    "city" => 'City',
    "state" => 'State',
    "zip_code" => 'Zip code',
    "country" => 'Country',
    "phone_number" => 'Phone number',
    'present_address' => 'Present address',
    'emergency_contact' => 'Emergency Contact',
    'relationship' => 'Relationship',
    'not_linked_yet' => 'Not linked yet',
    'allowance' => 'Allowance',
    'total_allowance' => 'Total Allowance',
    'earning_rate' => 'Earning rate',
    'availability' => 'Availability',
    'taken' => 'Taken',
    'earned' => 'Earned',
    'leave_allowance' => 'Leave allowance',

    'punch_in' => 'Punch In',
    'punch_out' => 'Punch Out',
    'punch_in_note' => 'Punch In note',
    'punch_out_note' => 'Punch Out note',
    'punch_in_note_here' => 'Punch In note here',
    'punch_out_note_here' => 'Punch Out note here',
    'punch_in_date_and_time' => 'Punch In date & time:',
    'punched_in_date_and_time' => 'Punched In date & time:',
    'punch_out_date_and_time' => 'Punch Out date & time:',

    'not_available' => 'N/A',
    'is_manager' => 'Is manager',
    "activate" => "Activate",
    "deactivate" => "De-activate",
    "move" => "Move",
    "department_manager_role_note" => "Want to disallow the employee for “Department Manger” responsibility? Then please {edit} the department of the employee and assign another user.",

    // Employee
    'employees' => 'Employees',
    'add_department' => 'Add department',
    'department_name' => 'Department name',
    'move_employee' => 'Move employee',
    'select_employee' => 'Select employee',
    'choose_all_employee' => 'Choose all employee',
    'manager' => 'Manager',
    'no_employees_found' => 'No employees found',
    'no_employees_warning' => 'No employees found in this department',
    'available_departments' => 'Available department',
    'do_you_want_to_move_employee' => 'Do you want to move employee?',
    'you_are_going_to_deactivate_a_department' => 'You are going to deactivate a department.',
    'you_are_going_to_activate_a_department' => 'You are going to activate a department.',
    'remove_from_employee_list' => 'Remove from employee list',
    'add_to_employee_list' => 'Add to employee list',
    'present' => 'Present',
    'not_yet_added' => 'Not yet added',
    'you_are_going_to_change_the_department_of_selected_employees' => 'You are going to change the department of selected employees!',
    'you_are_going_to_change_the_work_shift_of_selected_employees' => 'You are going to change the work shift of selected employees!',

    // Settings
    'company_info' => 'Company info',
    'company_name' => 'Company name',
    'type_your_company_name' => 'Type your company name',
    'company_logo' => 'Company logo',
    'change_logo' => 'Change logo',
    'company_icon' => 'Company icon',
    'company_banner' => 'Company banner',
    'change_icon' => 'Change icon',
    'change_banner' => 'Change banner',
    'recommended_company_logo_size' => '(Recommended size: 210 x 50 px)',
    'recommended_company_icon_size' => '(Recommended size: 50 x 50 px)',
    'recommended_company_banner_size' => '(Recommended size: 1920 x 1080 px)',
    'language' => 'Language',
    'date_and_time_setting' => 'Date and time setting',
    'time_zone' => 'Time zone',
    'currency_setting' => 'Currency setting',
    'currency_symbol' => 'Currency symbol',
    'currency_symbol_note' => 'PDF Payslip will not support all currency symbols. We recommend using currency codes like USD, EUR, NOK, etc.',

    'back_to_multi_tenant' => 'Back to multi-tenant',
    'activity_log' => 'Activity Log',
    'do_not_show_in_employee_list' => 'Do not show in employee list',
    'others' => 'Others',
    'employee_invitation' => 'Employee invitation',
    'employee_terminated' => 'Employee terminated',
    'csrf_token_mismatch_message' => 'Maybe you are in a frame. Please remove the frame and try again.',

    // Attendance
    'attendances' => 'Attendances',
    'attendance_details' => 'Attendance Details',
    'search_and_select_an_employee' => 'Search and select an employee',
    'punch_in_date' => 'Punch in date',
    'punch_in_time' => 'Punch in date and time',
    'punch_out_date' => 'Punch out date',
    'punch_out_time' => 'Punch out date and time',
    'reason_note_for_manual_entry' => 'Reason note for manual entry',
    'you_must_punch_out_message' => 'You must punch out to punch in.',
    'conflict_with_previous_attendances' => 'Conflict with previous attendances.',
    'conflict_with_previous_working_shift_time' => 'Conflict with previous working shift times.',
    'punched_in_successfully' => 'Punched in successfully.',
    'punched_out_successfully' => 'Punched out successfully.',
    'you_dont_punch_in_yet' => 'You do not punch in yet.',
    'employee_is_punched_in_message' => 'Employee is punched In in previous date.',
    'employee_field_is_required' => 'The employee field is required.',
    'punch_in_and_out_time_difference_message' => 'Punch in and out time difference must be less than 24 hours.',
    'total_approved_hours' => 'Total approved hours:',
    'in_time' => 'In time:',
    'out_time' => 'Out time:',
    'see_rejected' => 'See rejected',
    'show_rejected_attendances' => 'Show rejected attendances',
    'filter_data_which_are_rejected' => 'Filter data table for rejected requests',
    'already_requested_for_update_this_attendance' => 'Already requested for update this attendance',
    'punch_in_out_alert' => 'Punch In/Out alert',
    'punch_in_out_alert_suggestion' => 'System will show a popup message in defined interval if a user does not punch in or out according to his work shift.',
    'punch_in_out_interval' => 'Punch alert interval',
    'in_seconds' => '(In Seconds)',
    'you_have_not_punched_out' => 'You have not punched out!',
    'you_have_not_punched_in' => 'You have not punched in!',

    'yesterday' => 'Yesterday',
    'tomorrow' => 'Tomorrow',
    'this_week' => 'This week',
    'last_week' => 'Last week',
    'next_week' => 'Next week',
    'this_month' => 'This month',
    'last_month' => 'Last month',
    'next_month' => 'Next month',

    'punched_out' => 'Punched Out',
    'request_type' => 'Request type',
    'total_hours' => 'Total hours',

    'total' => 'Total',
    'worked' => 'Worked',
    'balance' => 'Balance',
    'daily_log' => 'Attendance Log',
    'punched_in' => 'Punched in',
    'preference' => 'Preference',
    'definitions' => 'Definitions',
    'punch_in_time_tolerance' => 'Punch in time tolerance',
    'auto_approval' => 'Auto approval',
    'enabled_auto_approval_suggestion' => 'In the enabled state, all the attendance request would be approved automatically without any reviews. As default, the app considers all employee for approval. Note that its possible to manage employees for auto approval.',
    'disabled_auto_approval_suggestion' => 'In disabled state all attendance request would be reviewed by the responsible user. The user will get notification for any attendance request.',
    'enabled_biometric_suggestion' => 'In the enabled state, attendance logs will be read from the biometric devince. You need to set the required fields to get the device connected.',
    'disabled_biometric_suggestion' => 'In disabled state, biometric device is disconnected from the system',
    'manage_audience_message' => 'Add role/user to whom you don\'t want review attendance request.',
    'manage_audience' => 'Manage audience',
    'by_role' => 'By role',
    'by_user' => 'By User',
    'choose_all' => 'Choose all',
    'punch_in_time_tolerance_recommendation' => 'The adjustment considers the punch in time based on a work shift.',
    'minutes' => 'Minutes',
    'early' => 'Early',
    'late' => 'Late',
    'work_availability_definition' => 'Work availability definition',
    'percentage' => 'Percentage',
    'work_availability_definition_recommendation' => 'The attendance percentage that defines an employee Good or Bad.',
    'good' => 'Good',
    'bad' => 'Bad',
    'before_on_time' => 'Before on time',
    'on_time_tolerance' => 'On time to tolerance',
    'after_tolerance' => 'After tolerance',
    'equal_to_or_above_of_percentage' => 'Equal or above of the percent',
    'bellow_percentage' => 'Bellow the percent',
    'behavior' => 'Behavior',
    'not_yet' => 'Not yet',
    'multi' => 'Multi',
    'single' => 'Single',
    'entry' => 'Entry',
    'auto' => 'Auto',
    'manual' => 'Manual',
    'in-note' => 'In note',
    'out-note' => 'Out note',
    'attendance_note' => 'Attendance note',
    'pending' => 'Pending',
    'entry_date' => 'Entry Date',
    'request_date' => 'Request date',
    'approve' => 'Approve',
    'reject' => 'Reject',
    'attendance_request_has_been_sent_successfully' => 'Attendance request has been sent successfully.',
    'out_time_existing_warning' => 'You cant send attendance request with out time without punch out of your attendance.',
    'already_requested_for_change' => 'You have already requested for a change on this attendance.',
    'not_approve_for_change_warning' => 'Request for change can only be made on approved attendance.',
    'change_log' => 'Change log',
    'note' => 'Note',
    'you_dont_have_permission_to_login' => 'You don\'t have permission to login.',
    'add_note_here' => 'Add note here',
    'this_work_shift_already_have_attendance' => 'This work shift can\'t be :action. It has attendances.',
    'you_are_going_to_cancel_a_attendance' => 'You are going to cancel a attendance',
    'you_are_going_to_reject_a_attendance' => 'You are going to reject a attendance',
    'you_are_going_to_approve_this_attendance' => 'You are going to approve this attendance',
    'incorrect_delivery_credential' => 'The email configuration you have added is incorrect.',
    'still_pending' => 'Still pending',
    'last_updated' => 'Last updated',
    'has_changed' => 'has changed',
    'has_added' => 'has added',
    'by' => 'by',
    'note_for' => 'Note for',
    'approve_and_close' => 'Approve & close',
    'reject_and_close' => 'Reject & close',
    'at_date' => 'At {date}',
    'action_by' => '{action} by',
    'hr' => 'HR',
    'added' => 'Added',
    'this_year' => 'This year',
    'last_year' => 'Last year',
    'average_behavior' => 'Average Behaviour',
    'total_active_hour' => 'Total active hour',
    'total_schedule_hour' => 'Total schedule hour',
    'total_work_availability' => 'Total work availability',
    'hour_short_form' => 'h',
    'lack' => 'Lack',
    'extra' => 'Extra',
    'days' => 'Days',
    'on_leave' => 'On leave',
    'previous' => 'Previous',
    'next' => 'Next',
    'back' => 'Back',
    'absent' => 'Absent',
    'attendance_can_not_be_applied_in_different_day' => 'Attendance can not be applied in different day!',
    'this_workshift_is_read_only_due_to_attendance_history' => 'This work shift is read only due to attendance history!',
    'view_workshift' => 'View Workshift',
    'punch_in_only_time' => 'Punch in time',
    'punch_out_only_time' => 'Punch out time',
    'good_morning' => 'Good morning',
    'monthly_time_log' => 'Monthly time log',
    'time_log' => 'Time log',
    'daily' => 'Daily',
    'you_did_not_punch_in' => 'You did not punch in yet!',
    'over_time' => 'Over time',
    'shortage_time' => 'Shortage time',
    'worked_time' => 'Worked time',
    'total_schedule_time' => 'Total schedule time',
    'total_leave_allowance' => 'Total leave allowance',
    'total_leave_taken' => 'Total leave taken',
    'total_leave_available' => 'Total leave available',
    'leave_request_pending' => 'Leave request pending',
    'total_employees' => 'Total employees',
    'total_departments' => 'Total departments',
    'total_leave_requests' => 'Total leave requests',
    'on_leave_today' => 'On leave today',
    'by_designation' => 'By Designation',
    'by_department' => 'By Department',
    'by_employment_status' => 'By employment status',
    'on_working_today' => 'On working today',
    'employee_statistics' => 'Employee statistics',
    'regular_work_shift' => 'Regular Work Shift',
    'working_shift_details' => 'Working shift details',
    'change_work_shift' => 'Change Work-Shift',
    'change_department' => 'Change Department',
    'confirm_employee_terminate' => 'Confirm Employee Terminate',

    //Leave
    'leaves' => 'Leaves',
    'is_enabled' => 'Is enabled',
    'enabled' => 'Enabled',
    'is_earning_enabled' => 'Allow monthly earning',
    'you_cant_update_leave_type_if_the_type_already_has_leave_applied' => 'You cant update leave type if the type already has leave_applied',
    'leave_label' => 'Leave',
    'leave_availability' => 'Leave availability',
    'assign_leave' => 'Assign leave',
    'paid_leave' => 'Paid Leave',
    'all_leaves' => 'All Leaves',
    'leave_status' => 'Leave Status',
    'leave_request' => 'Leave Request',
    'leave_period' => 'Leave Period',
    'leave_type' => 'Leave Type',
    'leave_settings' => 'Leave Settings',
    'leave_duration' => 'Leave duration',
    'leave_attachment_allowed_file_types' => 'Allowed file types: jpeg, jpg, gif, png, pdf, zip. (Max file size is 2MB)',
    'response_log' => 'Response log',
    'applied_by' => 'Applied by',
    'assigned_by' => 'Assigned by',
    'apply_date' => 'Apply date',
    'apply_between' => 'Apply between',
    'see_leave_requests' => 'See leave requests',
    'leave_taken' => 'Leave approved',
    'approved' => 'Approved',
    'upcoming' => 'Upcoming',
    'total_leave_availability' => 'Total leave available',
    'leave_type_earning' => 'Leave type earning',
    'leave_note' => 'Leave note',
    'reason-note' => 'Reason note',
    'leave_year_start_from' => 'Leave will start from the month of',
    'any_type_of_change_will_be_effected_from_next_day' => 'Any type of change will be effective on the next day.',
    'employees_for_allowance' => 'Employees for allowance',
    'add_employee_status_message' => 'Add employment status to allow employee for auto allowance',
    'for_unpaid_leave' => 'For unpaid leave',
    'for_paid_leave' => 'For paid leave',
    'single_level_suggestion' => 'Only main manager can response to leave request',
    'request_approval_type' => 'Request approval type',
    'multi_level' => 'Multi level',
    'single_level' => 'Single level',
    'allow_bypass_suggestion' => 'In the enable state all managers can bypass the leave request',
    'allow_bypass' => 'Allow bypass',
    'manage_leave_audience_message' => 'Add role/user to whom you don\'t want review leave request.',
    'special_audience' => 'Special audience',

    'duration_type' => 'Duration type',
    'employees_on_leave' => 'Employees on leave',
    'single_day' => 'Single day',
    'multi_day' => 'Multi day',
    'total_leave_hours' => 'Total leave hours',
    'first_half' => 'First half',
    'last_half' => 'Last half',
    'hours' => 'Hours',
    'day' => 'Day',
    'monthly_earning' => 'Monthly earning',
    'response_note' => 'Response note',
    'bypassed_to_manager' => 'Bypass the request to senior manager',
    'update_status' => 'Update status',
    'half_days' => 'Half days',
    'half_day' => 'Half day',
    'hour' => 'Hour',
    'not_added' => 'Not Added',
    'already_bypassed' => 'Already by passed to senior manager',
    'nothing_to_change' => 'Nothing To Change',
    'date_range' => 'Date range',
    'employee_leave' => 'Employee leave',
    'allowance_policy' => 'Allowance Policy',
    'user_leave_cant_be_less_than_taken_amount' => 'Leave amount can\'t be less than taken amount of the employee. Which is :taken',
    'cant_delete_employee_leave_type_warning' => 'You can\'t delete this employee leave allowance. It\'s has leaves',
    'no_available_leave' => 'No available leaves for this employee',
    'remove' => 'Remove',
    'user_leave' => 'User Leaves',
    'request_leave' => 'Request Leave',
    'can_not_delete_used_leave_type' => 'Can not delete! Leave type is in use.',
    'show_rejected_leaves' => 'Show rejected leaves',
    'multi_level_leave_approval_note' => 'Need multi approval by Department manager based on department hierarchy or single approval by Manager/App admin.',
    'single_level_leave_approval_note' => 'Only one approval needed by any Department manager to admin user.',

    //Leave assign exceptions
    'leave_is_not_available_on_weekend' => 'Leave is not available in weekend',
    'time_not_match_with_work_shift' => 'Leave time not match with work shift time',
    'requested_amount_is_greater_then_available_leaves' => 'Request amount is greater than available leaves',
    'leave_is_not_available' => 'Leave is not available',
    'Leave_time_exists_in_previous_leaves' => 'Leave already exists on this date',
    'Leave_time_exists_in_previous_leaves_request' => 'A Leave request is pending on this date',
    'cannt_add_Leaves_on_holiday' => 'Can\'t add leaves on holiday',
    'cant_delete_designation' => 'Can not delete! Designation has active users.',
    'cant_delete_employment_status' => 'Can not delete! Employment Status has active employees.',
    'reviewed' => 'Reviewed',
    'bypassed' => 'Bypassed',
    'leave_must_assigned_in_current_leave_year' => 'Leave must assigned in current leave year!',
    'how_leave_settings_work_message' => 'To understand how leave settings work, please checkout the {link}.',
    'leave_carry_forward_message' => 'Remained leave will not carry forward to next leave year.',

    //import
    'set_credentials' => 'Set imported database credentials',
    'db' => 'DB',
    'import_from_database' => 'Import From Database',
    'start' => 'Start',

    //Setup
    'database_configuration' => 'Database Configuration',
    'install' => 'Install',
    'db_connection' => 'Database connection',
    'database_hostname' => 'Database hostname',
    'database_port' => 'Database port',
    'database_name' => 'Database name',
    'database_username' => 'Database username',
    'database_password' => 'Database password',
    'admin_login_details' => 'Admin login details',
    'password_requirements' => 'Password requirements',
    'mysql' => 'MySQL 5.6+',
    'pgsql' => 'PostgreSQL 9.4+',
    'sqlsrv' => 'SQL Server 2017+',
    'app_installed_successfully' => 'App installed successfully',
    'general_configuration' => 'General Configuration',
    'required_environments' => 'Required Environments',
    'purchase_code' => 'Purchase code',
    'code' => 'Code',
    'invalid_purchase_code' => 'Invalid purchase code.',

    //Update
    'server_requirements' => 'Server Requirements',
    'server_permission_errors' => 'Please fix server and permissions error to proceed.',
    'php' => 'Php',
    'version_required' => 'Version {number} required',
    'environment' => 'Environment',
    'database_label' => 'Database',
    'attention' => 'Attention',
    'server_permission_required' => '{replace} required server write permission to install and run the apps. You can remove write permission of .env after installation.',
    'no_updates_found' => 'No update found.',
    'subject_for_this_email' => 'subject for this email',
    'please_update_your_php_version_to_number' => 'Please update your php version to :number',
    'public_directory_must_be_writeable_to_update_the_app' => 'Server must have the permission to write in root directory of app and public directory in order to update the app.',
    'install_zip_extension' => 'Zip extension is required in order to update the apps.',
    'please_install_version_first' => 'Please install version :number first.',
    'version_installed_successfully' => ':version installed successfully.',
    'update_warning' => '1. Please backup all files and database before start the installation of updates (including language files if you are using custom_lang.php file to overwrite translation text) and review the changelog.
                    <br> 2. You must install the previous versions to update the new version.',
    'this_will_update_entire_app' => 'This action will update the app to the selected version.',
    'no_updates_available' => 'No updates available.',
    'please_complete_the_first_step' => 'Please complete the first step.',
    'The user who will receive the notification' => 'The user who will receive the notification.',
    'Logo of the app' => 'Logo of the app',
    'Name of the app' => 'Name of the app',
    'The user who performed the action' => 'The user who performed the action',
    'The resource name of the event' => 'The resource name of the event',
    'Page link of resource' => 'Page link of resource',
    'Invitation url for the user' => 'Invitation url for the user',
    "We'll stop sending you this type of email" => "We'll stop sending you this type of email",
    'You are using version' => 'You are using :version.',
    'no_new_update_found' => 'No new update found.',

    //Models
    'setting' => 'Setting',

    //salary
    'effective_date' => 'Effective date',
    'effective_date_must_be_getter_than_now' => 'Effective date must be getter than now',
    'minimum_salary' => 'Minimum salary',
    'maximum_salary' => 'Maximum salary',
    'increment' => 'Increment',
    //Payroll
    'payroll' => 'Payroll',
    'payrun' => 'Payrun',
    'payslip' => 'Payslip',
    'payroll_settings' => 'Payroll Settings',
    'payrun_period' => 'Payrun period',
    'payrun_type' => 'Payrun Type',
    'net_salary' => 'Net salary',
    'monthly' => 'Monthly',
    'biweekly' => 'Biweekly',
    'hourly' => 'Hourly',
    'consider_attendance' => 'Consider attendance',
    'payslip_generation' => 'Payslip generation',
    'generating_day' => 'Generating day',
    'generating_day_label' => 'Generating day (n\'th day of month)',
    'monthly_payrun_note' => 'In the enabled state, if employees have no attendance log then the salary will be deducted based on the daily rate.',
    'default_payrun' => 'Default Payrun',
    'badge_value' => 'Deductions/Allowance',
    'payrun_generating_type' => 'Payrun generating type',
    'none' => 'None',
    'eligible_audience' => 'Eligible Audience',
    'all_user' => 'All user',
    'restricted_user' => 'Restricted User',
    'who_are_not_allowed_for_default_payrun' => 'Who are not allowed for the  default payrun?',
    'who_are_not_allowed_for_default_badge_value' => 'Who are not allowed for the  default badge value?',
    'department_preference' => 'Department preference',
    'user_preference' => 'User preference',
    'how_payrun_work' => 'How payrun works?',
    'beneficiary' => 'Beneficiary',
    'beneficiary_badge' => 'Beneficiary badge',
    'badge' => 'Badge',
    'beneficiary_type' => 'Beneficiary type',
    'deduction' => 'Deduction',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'beneficiary_badge_settings' => 'Beneficiary badge settings',
    'default_payrun_is_not_added_yet' => 'Default Payrun is not added yet.',
    'add_default_payrun_setting_first' => 'Please add default Payrun Setting first.',
    'back_to_payrun' => 'Back to payrun',
    'manual_payrun' => 'Create payrun',
    'audience' => 'Audience',
    'customized' => 'Customized',
    'executable_month' => 'Executable month',
    'executable_month_year' => 'Executable month & year',
    'confirm_and_run' => 'Confirm & Run',
    'always_run_for_the_last_month' => 'Always run for the last month',
    'who_are_allowed_for_payrun' => 'Who are allowed for the payrun?',
    'manual_payrun_audience_message' => 'You can add users by Department or Employee directly to generate payslip.
     if you select any department then it takes only active employees from that department. if you need to select inactive or terminated employees, you can select from by user filed',
    'payrun_and_badge' => 'Payrun and Badge',
    'why_payrun_and_badge' => 'Why Payrun & Badge?',
    'edit_payrun' => 'Edit Payrun',
    'add_payrun' => 'Add Payrun',
    'edit_allowance' => 'Edit Allowance',
    'edit_deduction' => 'Edit Deduction',
    'payrun_setting' => 'Payrun Setting',
    'send_filtered_payslip' => 'Send Filtered Payslip',
    'send_monthly_payslip' => 'Send Monthly Payslip',
    'sent' => 'Sent',
    'resend' => 'Resend',
    'send' => 'Send',
    'show_conflicted_payslip' => 'Show Conflicted Payslip',
    'send_last_month_payslip' => 'Send last month payslip.',
    'payrun_audience' => 'Payrun Audience',
    'payroll_summery' => 'Payroll Summery',
    'transmittal' => 'Transmittal',
    'for_signature' => 'For Signature',
    'consolidation' => 'Consolidation',
    'transmittal_list' => 'Transmittal List',
    'transmittal_description' => 'Manage payroll transmittals and document processing.',
    'transmittal_coming_soon' => 'Transmittal functionality is coming soon.',
    'for_signature_list' => 'For Signature List',
    'for_signature_description' => 'Manage documents requiring signatures.',
    'for_signature_coming_soon' => 'For Signature functionality is coming soon.',
    'consolidation_list' => 'Consolidation List',
    'consolidation_description' => 'Manage payroll consolidation and reporting.',
    'consolidation_coming_soon' => 'Consolidation functionality is coming soon.',
    'view_pdf' => 'View PDF',
    'download_pdf' => 'Download PDF',
    'filter_data_which_are_conflicted' => 'Filter data which are conflicted.',
    'how_badge_value_work' => 'How badge value work?',
    'how_payrun_audience_work' => 'How payrun audience work?',
    'by_default_all_payrun_and_beneficiary_badges_is_set_from_default_setting' => 'By default all payrun and beneficiary badges is set from default {setting}',
    'you_can_individually_update_or_change_these_values_from_the_edit_option' => 'You can individually update or change these values from the edit option.',
    'default_payrun_is_applicable_to_generate_payslip_for_all_employees_whenever_it_execute_from_payrun_module' => 'Default payrun is applicable to generate payslip for all employees (Except those are updated individually) whenever it execute from {payrun} module.',
    'you_can_set_payrun_individually_over_the_default_from_the_employees_details' => 'You can set payrun individually over the default from the {employees} details.',
    'you_can_set_beneficiary_individually_over_the_default_from_the_employees_details' => 'You can set beneficiary individually over the default from the {employees} details.',
    'create_badge_for_allowance_or_deduction_from_beneficiary_badge_module' => 'Create badge for allowance or deduction from {beneficiary_badge} module.',
    'select_badge_and_assign_a_value_which_will_applicable_for_all_employees_while_execute_payrun' => 'Select badge and assign a value that will applicable for all employees (Except those are updated individually) while execute payrun.',
    'you_can_also_update_beneficiaries_in_payslip_generated_for_every_employee' => 'You can also update beneficiaries in {payslip} generated for every employee.',
    'selecting_all_will_execute_payrun_for_every_employee_while_executing_default_payrun' => 'Selecting All User will execute payrun for every employee while executing default payrun.',
    'selecting_restricted_user_will_prevent_execute_payrun_for_selected_employee_or_department_while_executing_default_payrun' => 'Selecting restricted user will prevent execute payrun for selected employee or department while executing default payrun.',
    'default_payrun_settings_not_set' => 'Default payrun settings not added yet',
    'all_good_just_have_quick_read' => 'All good, Just have a quick read!',
    'default_payrun_start_notice' => 'You are going to add default payrun based on the settings or employee preference.',
    'have_look_generated_default_payrun' => 'Please have a look on payrun generating for last month.',
    'make_your_own' => 'Please make your own',
    'also_can' => 'Also can',
    'otherwise_run_default_payrun' => 'Otherwise run the default payrun',
    'includes_employee' => 'Includes {count} employees',
    'weekly' => 'Weekly',
    'earning' => 'Earning',
    'default' => 'Default',
    'followed_by' => 'Followed by {setting}',
    'and_payrun_settings' => 'and payrun settings',
    'mixed' => 'Mixed',
    'period' => 'period',
    'consider_type' => 'consider type',
    'multiple_time_period' => 'Multiple time period',
    'send_this_payslip_to_employee' => '{send} this payslip to {employee}.',
    'employee_payslip' => 'Employee payslip',
    'payslip_has_been_sent_successfully' => 'Payslip has been sent successfully.',
    'run' => 'Run',
    'payrun_date' => 'Payrun date',
    'show_conflicted' => 'Show conflicted',
    'no_payrun_settings_warning' => 'To run default payrun you must set up payrun settings. Click {location} to add payrun settings.',
    'no_payrun_settings_found' => 'To run default payrun you must set up payrun settings.',
    'show_conflicted_payrun' => 'Show conflicted payrun',
    'restore_to_default' => 'Restore to default',
    'edit_badge' => 'Edit Badge',
    'payslip_sending_has_started' => 'Payslip sending has started.',
    'basic_salary' => 'Basic salary',
    'net_payable_salary' => 'Net payable salary',
    'created_at' => 'Created at',
    'not_yet_sent' => 'Not yet sent',
    'sent_to_employee' => 'Sent to {count} employee',
    'resend_payslip' => 'Resend payslip',
    'send_payslip' => 'Send payslip',
    'no_payruns_found' => 'no payruns found',
    'partially_sent' => 'Partially sent',
    'conflicted_payslip' => 'Conflicted Payslip',
    'payslip_sent' => 'Payslip Sent',
    'total_payslip' => 'Total Payslip',
    'can_not_delete_used_badge' => 'Can not delete used badge!',
    'can_not_deactivate_used_badge' => 'Can not deactivate used badge!',
    'sending_filtered_payslip' => 'Sending filtered payslip.',
    'sending_for_last_month' => 'Sending for last month.',
    'sending_all_payslip_of_this_payrun' => 'Sending all payslip of this payrun.',
    'payslip_has_started_sending' => 'Payslip has started sending.',
    'payslips_conflicted_number_of_employees' => 'Payslip of {number} employees are conflicted',
    'restricted_user_for_payrun' => 'Restricted user for payrun.',
    'by_default_all_are_eligible' => 'By default all are eligible',
    'restricted_user_for_beneficiary_badge' => 'Restricted user for beneficiary badge.',
    'employment_status_preference' => 'Employment Status preference',
    'payrun_audience_settings' => 'Payrun audience settings',
    'conflicted_payslip_for_employee' => 'Conflicted payslip for employee',
    'view_confliction' => 'View confliction',
    'generating' => 'Generating',
    'generated_of' => 'generated of',
    'payrun_execution_completed' => 'Payrun execution completed.',
    'no_payslip_found' => 'No payslip found!',
    'beneficiary_amount' => 'Beneficiary Amount',
    'total_deduction' => 'Total Deduction',
    'total_earning' => 'Total Earning',
    'based_on' => 'Based on',
    'payslip_for' => 'Payslip for',
    'allowances' => 'Allowances',
    'deductions' => 'Deductions',
    'details' => 'Details',
    'please_filter_your_payslip_first' => 'Please filter your payslip first.',
    'similar_payslips' => 'similar payslips',
    'drop' => 'Drop',
    'payrun_id' => 'Payrun ID',
    'no_conflicts' => 'No Conflicts!',
    'no_worries' => 'No Worries!',
    'this_payslip_has_no_confliction_with_other_payslips' => 'This payslip has no confliction with other payslips.',
    'payslip_confliction_message' => '{count} payslips here for similar payrun period. You can Drop or Keep these as you want.',
    'consider_overtime' => 'Consider Overtime',
    'overtime_included' => 'Overtime Included',
    'overtime_excluded' => 'Overtime Excluded',
    'exclude' => 'exclude',
    'include' => 'include',
    'manage_conflict' => 'Manage Conflict',
    'included_overtime' => 'Included Overtime',
    'excluded_overtime' => 'Excluded Overtime',
    'attention_please' => 'Attention please!',
    'customized_date_multi_month_warning_message' => 'You cannot generate payslip for multiple months, You can only generate payslip for a single month',
    'no_user_found' => 'No user found',
    'confirm_password' => 'Confirm Password',
    'working_shift_removed_from_department' => 'Working Shift removed from the department.',

    'database_credential_error' => 'Incorrect database credential',

    'working_shift_update_note' => 'Any type of change will be effective on the next day.',
    'comment' => 'Comment',
    'invalid_date_or_not_match_with_working_shift' => 'Invalid date or not match time with working Shift',
    'pending_request' => 'Pending request',
    'total_request_hours' => 'Total request hours',
    'single_day_request' => 'Single day request',
    'multi_day_request' => 'Multi day request',
    'upcoming_leave' => 'Upcoming leave',
    'overtime_will_be_calculated_after_the_end_of_the_total_schedule_time' => 'Overtime will be calculated after the end of the Total schedule time.',
    'checked_to_get_access_for_all_user' => 'Checked to get access for all users',
    'payee' => 'Payee',
    'view_all_payslips' => 'View payslips',
    'manage_users' => 'Manage Users',
    'for_payrun' => 'For Payrun',
    'for_beneficiary_badge' => 'For Beneficiary Badge',
    'restriction_note' => 'Restriction Note',
    'user_restriction_note' => 'If you want to restrict some users for the default payrun settings, then please add users for',
    'by_default_all_users_are_eligible_for' => 'By default, all users are eligible for',
    'payrun_and_beneficiary_badges' => 'Payrun and Beneficiary badges.',
    'department_manager_manage_user_message' => 'The department manager cannot be managed from here. Please go to the {department} module and change the manager of a department as you need.',
    'has_attendance' => 'Has Attendance',
    'import' => 'Import',
    'import_employee' => 'Import Employees',
    'download' => 'Download',
    'payslip_note' => 'Payslip note',
    'add_payslip_note_here' => 'Add payslip note here',
    'optional' => 'optional',
    '_download' => 'Download',

    // CSV Import //
    'csv_format_guideline' => 'CSV format guideline',
    'csv_format_guide' => 'Format your CSV the same way as the sample file.',
    'csv_column_separation_guide' => 'Your CSV columns should be separated by commas, not semicolons or any other characters.',
    'csv_column_guide' => 'The names and the number of the column in your CSV should be the same as the sample.',
    'csv_huge_data_guide' => 'Recommended CSV file should not contain more than 500 rows with default server configuration.',
    'csv_error_handle_guide' => 'For resolving the error download the error file and correct the incorrect cells and import that file again through .csv format.',
    'csv_after_import_message' => 'All imported employees will receive an email to reset their password in order to log in except the terminated employee.',
    'csv_column_data_format' => 'The columns : department, designation, employment_status and roles value must be the same value that exists on the application. For the gender column three option available [male, female, other], for multiple roles use only comma(,) to separate roles and the joining_date column must be the date format of YYYY-MM-DD',
    'csv_download_label' => 'We would like to provide you a sample .CSV file - ',

    //Import
    'csv_required_field_guide' => "Required field's (name, email, gender, employee_id, department, designation, employment_status, roles) column cell must not be empty.",
    'partially_imported' => ':subject partially imported.',
    'has_been_imported_successfully' => ':subject has been imported successfully.',
    'is_invalid_message' => 'The selected :subject is invalid.',
    'data_imported_successfully' => 'Data imported successfully.',
    'partially_data_imported' => 'Data imported partially!',
    'after_employee_import_message' => 'All imported employees will receive an email to reset their password in order to log in except the terminated employee.',
    'errors_found' => 'Errors found',
    'maximum_execution_time_exceeded' => 'Maximum execution time exceeded!',
    'maximum_row_exceeded_message' => 'CSV file can not contain more than 500 rows(employee) at a time!',

    'import_attendance' => 'Import attendance',
    'download_sample_file' => 'Download sample file',
    'csv_required_field_guide_attendance' => "Required field's (employee_id, in_time, out_time) column cell must not be empty.",
    'csv_column_data_format_attendance' => 'The column : employee_id value must be the same value that exists on the application and the in_time and out_time columns must be the date format of \'Y-m-d H:m:s\' (the hour should be 24hour format)',
    'after_attendance_import_message' => 'You will find all imported data in attendance module.',
    'please_filter_the_attendance_data_first' => 'Please filter the attendance data first.',
    'no_attendance_data_found' => 'No attendance data found.',
    'please_filter_the_leave_data_first' => 'Please filter the leave data first.',
    'no_leave_data_found' => 'No leave data found.',
    'this_employee_does_not_belongs_to_your_department' => 'This employee does not belongs to your department.',

    //Common
    'action_not_allowed_in_demo' => 'Action not allowed in demo version',
    'login_as' => 'Login as',
    'select_role' => 'Select role',
    'in_hours' => 'In hours',

    'test_email' => 'Test Email',
    'send_test_email' => 'Send test email',
    'email_address' => 'Email address',
    'message' => 'Message',
    'subject' => 'Subject',
    'email_sending_failed' => 'Email sending failed!',
    'email_sent_successfully' => 'Email sent successfully.',
    'email_setup_is_not_correct' => 'Email setup is not correct!',
    'use_default_logo' => 'Use default logo',
    'use_default_title' => 'Use default title',
    'use_default_address' => 'Use default address',
    'customize' => 'Customize',
    'payslip_logo' => 'Payslip logo',
    'payslip_title' => 'Payslip title',
    'payslip_settings' => 'Payslip settings',
    'payslip_address' => 'Payslip address',
    'includes_off_day' => 'Includes {count} off day',
    'includes_off_days' => 'Includes {count} off days',
    'attendance_export_title' => 'Export filtered attendances.',
    'attendance_export_message' => 'All filtered attendances of the data table will be exported in .xlsx format.',
    'leave_export_title' => 'Export filtered leaves.',
    'leave_export_message' => 'All filtered leaves of the data table will be exported in .xlsx format.',
    'export_last_month' => 'Export last month',
    'last_month_attendance_export_title' => 'Export last month\'s attendances.',
    'last_month_attendance_export_message' => 'All attendances of the last month will be exported in .xlsx format.',
    'export_all' => 'Export all',
    'all_asset_export_title' => 'Export assets.',
    'all_asset_export_message' => 'All asset data will be exported in .xlsx format.',
    'all_employees_attendance_export_title' => 'Export filtered attendances for all employees.',
    'all_employees_attendance_export_message' => 'All employees attendances of filtered data will be exported in .xlsx format.',
    'all_employees_leave_export_title' => 'Export filtered leaves for all employees.',
    'all_employees_leave_export_message' => 'All employees leaves of filtered data will be exported in .xlsx format.',
    'all_payslip_export_title' => 'Export all payslip.',
    'all_payslip_export_message' => 'All payslip data will be export in .xlsx format.',
    'export_as_excel' => 'Export as excel in .xlsx format.',
    'command' => 'Command',
    'cron_job' => 'Cron Job',
    'command_with_php_path' => 'Command with php path',
    'command_without_php_path' => 'Command without php path',
    'see_documentation' => 'See Documentation',
    'cron_job_description_message' => 'For executing scheduled jobs',
    'cron_job_setting_suggestion' => 'We are providing the cron job command and highly recommend to run it every minute(once per minute ****). Copy the command and insert it to your hosted server\'s crontab. For more help you can see the documentation.',
    'cron_job_setting_warning' => 'You must setup the cron job in your hosted server for assigning work shift, generating payslip, sending bulk emails, assigning leaves and renew holidays.',
    'export' => 'Export',
    'in_note' => 'In note',
    'out_note' => 'Out note',
    'request_note' => 'Request note',

    // Department
    'create_announcements' => 'Can create announcements',
    'view_announcements' => 'Can view announcements',
    'update_announcements' => 'Can update announcements',
    'delete_announcements' => 'Can delete announcements',
    'announcements' => 'Announcements',
    'announcement' => 'Announcement',
    'created_by' => 'Created by',
    'dashboard_announcement' => 'Dashboard Announcements',
    'announcement_details' => 'Announcement details',
    'you_can_set_the_announcement_only_for_specific_department_by_adding_them' => 'You can set the announcement only for the specific departments by adding them',

    //Geolocation and ip
    'geolocation_and_ip' => 'Geolocation & IP',
    'geolocation_settings' => 'Geolocation Setting',
    'ip_endpoint' => 'IP Endpoint',
    'api_endpoint' => 'Api Endpoint',
    'in_geolocation' => 'In Geolocation',
    'out_geolocation' => 'Out Geolocation',
    'geo_location_data' => 'Geolocation data',
    'google_map' => 'Google Map',
    'ip_geolocation' => 'IP Geolocation',
    'work_from_home' => 'Work From Home',
    'in_ip' => 'In IP',
    'out_ip' => 'Out IP',
    'chose_geolocation_service' => 'Chose geolocation service',
    'about_geo_location_api' => '{ip_geolocation} is an IP API service provider which is integrated with this app to get the IP and Geolocation data of an employee when Punching In. Create an account to this {website} for the api key which is must need to get the location data. Otherwise you will get only the IP.',
    'about_google_map_geo_location_service' => '{google_map} is a map and location service provider which is integrated with this app to get the Geolocation data of an employee when Punching In. Create an account to this {website} for the api key which is must need to get the location data. Otherwise you will get only the IP.',
    'about_mapbox_geo_location_service' => '{mapbox} is a map and location service provider which is integrated with this app to get the Geolocation data of an employee when Punching In. Create an account to this {website} for the api key which is must need to get the location data. Otherwise you will get only the IP.',

    'approve_all' => 'Approve all',
    'reject_all' => 'Reject all',
    'approve_all_attendance_request' => 'Approve selected attendance request',
    'reject_all_attendance_request' => 'Reject selected attendance request',
    'all_attendance_request_approve_message' => 'Selected all attendance request will be approved accept own requests!',
    'all_attendance_request_reject_message' => 'Selected all attendance request will be rejected accept own requests!!',
    'approve_all_leave_request' => 'Approve all selected leave request',
    'reject_all_leave_request' => 'Reject all selected leave request',
    'leave_bulk_action_message' => 'Leave bulk action will not applicable to your own request and will follow the leave policy and settings.',

    'document' => 'Document',
    'document_recommendation' => 'Document size allowed: 5MB. Document type allowed: png, jpg, jpeg, txt, pdf, doc, docx, csv. Please check file and file format before upload.',
    'add_new' => 'Add New',
    'add_document' => 'Add Document',
    'edit_document' => 'Edit Document',
    'added_by' => 'Added by',
    'total_attendance_today' => 'Total Attendance Today',
    'total_scheduled' => 'Total Scheduled',
    'total_employee' => 'Total Employee',
    'active_hour' => 'Active Hour',
    'employee_list' => 'Employee List',
    'this_department_has_no_employee' => 'This department has no employee!',
    'amount_in_days' => 'Amount (In days)',
    'important_announcement' => 'Important Announcement!',
    'version_update_message' => 'Your application is now support PHP 8.x version. We request you to upgrade your server\'s PHP version to 8.x immediately. Our future update will not be compatible with PHP 7.4 version any more.',

    'assets' => 'Assets',
    'maintenance' => 'Maintenance',
    'is_working' => 'Is Working',
    'asset_type' => 'Asset Type',
    'asset_code' => 'Asset Code',
    'asset_name' => 'Asset Name',
    'company_assets' => 'Company Assets',
    'company_asset' => 'Company Asset',
    'asset' => 'Asset',
    'preview_asset' => 'Preview Asset',
    'back_to_assets' => 'Back to Assets',
    'asset_types' => 'Asset Types',
    'company_asset_type' => 'Company Asset Type',
    'add_asset_type' => 'Add Assets Type',
    'edit_asset_type' => 'Edit Assets Type',
    'add_asset' => 'Add Asset',
    'edit_asset' => 'Edit Asset',
    'asset_serial_number' => 'Asset serial number',
    'assigned_employee' => 'Assigned employee',
    'select_assigned_employee' => 'Select assigned employee',
    'select_asset_type' => 'Select asset type',
    'serial_no' => 'Serial No.',
    'alert_area' => 'Alert area',
    'web' => 'Web',
    'on' => 'On',
    'system' => 'System',
    'can_not_delete_asset' => 'Can not delete asset!',
    'can_not_delete_used_asset_type' => 'Can not delete used asset type!',
    'alert_area_suggestion' => 'Selecting web will only show popup on browser tab and selecting system will show alert on your system.',
    'module_setting_suggestion' => 'Only selected items will be shown to the sidebar along with Dashboard and Settings.',
    'module_list' => 'Module List',
    'module_settings' => 'Module settings',
    'bank_details' => 'Bank details',
    'view_employee_bank_details' => 'View employee bank details',
    'create_employee_bank_details' => 'Create employee bank details',
    'update_employee_bank_details' => 'Update employee bank details',
    'delete_employee_bank_details' => 'Delete employee bank details',
    'bank_name' => 'Bank name',
    'bank_code' => 'Bank code',
    'branch_name' => 'Branch name',
    'account_title' => 'Account title',
    'account_holder_name' => 'Account holder name',
    'account_number' => 'Account number',
    'tax_payer_id' => 'Tax payer id',
    'break_time' => 'Break time',
    'take_a_break' => 'Take Break',
    'break_times' => 'Break times',
    'edit_break_time' => 'Edit break time',
    'duration' => 'Duration',
    'add_break_time' => 'Add Break Time',
    'back_to_workshift' => 'Back to WorkShift',
    'can_not_delete_used_break_time' => 'Can not delete used break time!',
    'break_time_started' => 'Break time started!',
    'break_time_ended' => 'Break time ended!',
    'end_break' => 'End Break',
    'start_break' => 'Start Break',
    'break' => 'Break',
    'end' => 'End',
    'enter_ip' => 'Enter IP',
    'ip_restriction' => 'IP Restriction',
    'ip_validation' => 'IP validation',
    'allow_ip' => 'Allow IP',
    'allowed_ip' => 'Allowed IP',
    'attendance_ip' => 'Attendance IP',
    'restrict_ip' => 'Restrict IP',
    'punch_in_ip_validation' => 'Restrict or Allow network IP for Punch In',
    'you_are_not_allowed_to_punch_in_from_this_ip' => 'You are not allowed to punch in from this IP!',
    'manage_break_time' => 'Manage Break Time',
    'not_on_break' => 'Not on break!',

    //Api response lang
    'login_successfully' => 'Login successfully',
    'logout_successfully' => 'logout_successfully',
    'you_are_not_punch_in' => 'You are not punched in',
    'you_are_already_punch_in' => 'You are already punched in',
    'already_on_break' => 'Already on break',

    'privacy_policy' => 'Privacy policy',
    'terms_and_conditions' => 'Terms & Conditions',
    'storage_configuration' => 'Storage Configuration',
    'storage_type' => 'Storage type',
    'aws_access_key' => 'AWS access key',
    'aws_secret_key' => 'AWS secret key',
    'aws_region' => 'AWS region',
    'aws_bucket_name' => 'AWS bucket name',
    'storage_setting' => 'Storage setting',
    'incorrect_storage_credential' => 'The storage configuration you have added is incorrect.',
    'storage_migration_note' => 'If you are a existing user and you want to migrate from system storage to Amazon S3, the you must have to follow the step.',
    'storage_migration_step1' => 'Please navigate to the Application\'s {path} folder.',
    'storage_migration_step2' => 'Upload all folders and files to your S3 bucket.' ,
    'storage_migration_step3' => 'After finish uploading, save the S3 credentials below and you are ready to go!' ,
    'important_note' => 'Important Note',
    'about_s3' => 'Use Amazon S3 for application\'s files and media storage.',
    'about_system_storage' => 'Use systems local storage for application\'s files and media storage(Default).',
    'timezone_preference' => 'Timezone Preference',
    'fixed_timezone_note' => 'Date and Time will be shown in selected timezone.',
    'device_timezone_note' => 'Date and Time will be shown based on the device timezone.',
    'time_zone_preference_note' => 'By default app automatically identify Timezone from the device and show date and time based on that Timezone. But now you can set a Fixed Timezone and the app will show time based on that Timezone.',
    'from_device' => 'From Device',
    'about_time_zone_preference' => 'About Timezone Preference',
    'fixed' => 'Fixed',
    'good_afternoon' => 'Good Afternoon',
    'good_evening' => 'Good Evening',
    'remember' => 'Remember',
    'csv_export_guide1' => 'You have the option to export all data, data from a single module, or data from multiple modules',
    'csv_export_guide2' => 'Editing the file may cause issues when trying to import',
    'import_/_export' => 'Import/Export',
    'select_type' => 'Select type',
    'all_data' => 'All data',
    'export_data_failure' => 'Export data failure',
    'are_you_sure_you_want_to_download_data' => 'Are you sure you want to download data',
    'export_file_saved_successfully' => 'Export file saved successfully!',
    'please_wait_until_progress_is_done' => 'Please wait until progress is done.',
    'answer' => 'Answer',
    'retry' => 'Retry',
    'modules' => 'Modules',
    'modules_employee' => 'Employee',
    'modules_leave' => 'Leave',
    'modules_attendance' => 'Attendance',
    'modules_work_shift' => 'Work shift',
    'modules_employment_history' => 'Employment history',
    'modules_designation_history' => 'Designation history',
    'modules_department_history' => 'Department history',
    'modules_leave_type' => 'Leave type',
    'modules_holiday' => 'Holiday',
    'export_in_progress' => 'Export in progress!',
    'export_file' => 'Export File',
    'make_an_export_request' => 'Make an export request',
    'export_file_in_progress' => 'Export file in progress',
    'are_you_want_to_re_request' => 'Are you want to retry for export?',
    'modules_salary_history' => 'Salary History',
    'modules_department' => 'Department',
    'modules_leave_status_history' => 'Leave status history',
    'leave_status_history' => 'Leave status history',

    // Biometric settings
    'biometric_setting' => 'Biometric Setting'

], include 'custom.php');
